#!/usr/bin/env node

// 调试连接字符串解析
import { PostgreSQLDriver } from './dist/database/driver.js';

console.log('🔍 调试 PostgreSQL 连接字符串解析...\n');

const driver = new PostgreSQLDriver();
const testSource = 'postgres:123456@tcp(***************:5432)/demo?charset=utf8mb4&parseTime=True&loc=Local';

console.log(`测试连接字符串: ${testSource}`);

try {
  const parsed = driver.parsePostgreSQLConnectionString(testSource);
  console.log('✅ 解析成功:');
  console.log('  主机:', parsed.host);
  console.log('  端口:', parsed.port);
  console.log('  用户:', parsed.user);
  console.log('  密码:', parsed.password);
  console.log('  数据库:', parsed.database);
  
  console.log('\n🔧 完整的连接选项:');
  console.log(JSON.stringify(parsed, null, 2));
  
} catch (error) {
  console.log('❌ 解析失败:', error.message);
}

// 测试数据库名提取
console.log('\n🔍 测试数据库名提取...');

function extractDatabaseName(source) {
  if (source.includes('@tcp(')) {
    // 类似 MySQL 格式: user:password@tcp(host:port)/database
    const match = source.match(/@tcp\([^)]+\)\/([^?]+)/);
    if (match) {
      return match[1];
    }
  }
  return null;
}

const dbName = extractDatabaseName(testSource);
console.log(`提取的数据库名: ${dbName}`);
