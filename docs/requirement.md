# 实现一个命令行工具
## 配置
config.yaml
```
db1:
 driver: mysql
 source: 'root:123456@tcp(127.0.0.1:3306)/threebody-admin?charset=utf8mb4&parseTime=True&loc=Local'

db2:
 driver: mysql
 host: 127.0.0.1
 port: 3306
 db: threebody-admin
 user: root
 password: 123456
 charset: utf8mb4
 
```
## 查询sql命令
使用tui形式实现
./dbtool query
进入查询界面, 界面采用现代化布局, 左侧显示数据库和表，上面显示sql输入框，下面显示查询后的数据
:query 聚焦查询
:db 聚焦数据库
:table 聚焦表
:commit 执行查询语句
:quit 退出tui
ctrl+c 退出tui
