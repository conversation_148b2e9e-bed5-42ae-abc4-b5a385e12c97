# 表排除功能使用指南

## 概述

dbtool 现在支持在数据库迁移过程中排除或仅包含特定的表。这个功能可以帮助你：

- 排除系统表、日志表、临时表等不需要迁移的表
- 仅迁移核心业务表
- 在不同环境间进行有选择性的数据同步
- 减少迁移时间和文件大小

## 配置方式

### 1. 配置文件中设置

在 `config.yaml` 中为数据库配置添加表过滤选项：

```yaml
# 排除特定表
source_db:
  driver: postgresql
  host: localhost
  port: 5432
  db: myapp
  user: postgres
  password: password
  excludeTables:
    - logs
    - sessions
    - cache_data
    - temp_uploads
    - audit_trail

# 仅包含特定表
target_db:
  driver: mysql
  host: localhost
  port: 3306
  db: myapp_mysql
  user: root
  password: password
  includeTables:
    - users
    - products
    - orders
    - categories

# 同时使用包含和排除
filtered_db:
  driver: postgresql
  host: localhost
  port: 5432
  db: myapp
  user: postgres
  password: password
  # 先包含这些表
  includeTables:
    - users
    - products
    - orders
    - logs
    - admin_logs
  # 然后从包含的表中排除这些
  excludeTables:
    - logs  # 排除普通日志，保留管理日志
```

### 2. 命令行参数

命令行参数会覆盖配置文件中的设置：

```bash
# 排除特定表
dbtool migrate --exclude logs,sessions,cache_data source_db target_db ./migrations

# 仅包含特定表
dbtool migrate --include users,products,orders source_db target_db ./migrations

# 同时使用（先包含，后排除）
dbtool migrate --include users,products,orders,logs --exclude logs source_db target_db ./migrations

# 包含数据迁移并排除表
dbtool migrate --include-data --exclude temp_table,log_table source_db target_db ./migrations
```

## 过滤规则

### 优先级

1. **命令行参数** 优先级最高，会完全覆盖配置文件设置
2. **includeTables** 优先于 excludeTables
3. **excludeTables** 在 includeTables 之后应用

### 处理逻辑

```
1. 获取数据库中的所有表
2. 如果设置了 includeTables，只保留这些表
3. 如果设置了 excludeTables，从结果中排除这些表
4. 返回最终的表列表
```

### 示例

假设数据库中有表：`users, products, orders, logs, sessions, temp_data`

```yaml
# 情况 1：只使用 excludeTables
excludeTables: [logs, sessions]
# 结果：users, products, orders, temp_data

# 情况 2：只使用 includeTables  
includeTables: [users, products, orders]
# 结果：users, products, orders

# 情况 3：同时使用
includeTables: [users, products, orders, logs]
excludeTables: [logs]
# 结果：users, products, orders
```

## 实际应用场景

### 1. 生产环境迁移

```yaml
prod_source:
  driver: mysql
  host: prod.example.com
  port: 3306
  db: production
  user: readonly_user
  password: password
  excludeTables:
    # 排除敏感数据
    - user_sessions
    - access_logs
    - error_logs
    - audit_trail
    # 排除临时数据
    - temp_uploads
    - cache_data
    - job_queue
```

### 2. 开发环境同步

```yaml
dev_target:
  driver: postgresql
  host: localhost
  port: 5432
  db: development
  user: postgres
  password: postgres
  includeTables:
    # 只同步核心业务表
    - users
    - products
    - orders
    - categories
    - inventory
```

### 3. PostGIS 数据库

```yaml
gis_database:
  driver: postgresql
  host: localhost
  port: 5432
  db: gis_app
  user: postgres
  password: postgres
  excludeTables:
    # 排除 PostGIS 系统表
    - spatial_ref_sys
    - geometry_columns
    - geography_columns
    - raster_columns
    - raster_overviews
```

## 命令行使用示例

```bash
# 基本排除
dbtool migrate --exclude logs,sessions prod_db dev_db ./migrations

# 仅迁移用户相关表
dbtool migrate --include users,user_profiles,user_settings prod_db dev_db ./migrations

# 包含数据但排除大表
dbtool migrate --include-data --exclude big_analytics_table,log_archive prod_db dev_db ./migrations

# 指定配置文件并使用过滤
dbtool migrate -c ./prod.yaml --exclude audit_logs,temp_data db1 db2 ./migrations
```

## 验证和测试

使用提供的测试脚本验证过滤功能：

```bash
# 运行表过滤测试
node test-exclude-tables.js

# 测试不同配置
node test-exclude-tables.js config.test.yaml
```

## 注意事项

1. **表名区分大小写**：表名匹配是精确匹配，请确保大小写正确

2. **依赖关系**：排除表时要注意外键依赖关系，避免引用完整性问题

3. **数据一致性**：使用 includeTables 时，确保包含了所有相关的关联表

4. **配置继承**：如果源数据库和目标数据库都有表过滤配置，它们会独立应用

5. **不存在的表**：过滤配置中包含不存在的表名不会导致错误，会被忽略

6. **通配符支持**：当前版本不支持通配符匹配，未来版本可能会添加此功能

## 性能优势

- **减少网络传输**：只处理需要的表，减少数据传输量
- **加快迁移速度**：跳过不必要的表，提升处理速度  
- **节省存储空间**：生成的迁移文件更小
- **提高安全性**：避免迁移敏感或不必要的数据

## 故障排除

如果过滤功能不如预期工作：

1. 检查表名拼写和大小写
2. 确认表确实存在于数据库中
3. 检查配置文件语法是否正确
4. 使用测试脚本验证配置
5. 查看程序输出的过滤信息日志

## 未来增强

计划在未来版本中添加：

- 通配符模式匹配（如 `temp_*`）
- 正则表达式支持
- 基于表类型的过滤（如仅视图、仅表）
- 交互式表选择界面