#!/usr/bin/env node

// 表格功能演示脚本
import React, { useState } from 'react';
import { render, Box, Text, useInput } from 'ink';
import { ResultTable } from './dist/components/ResultTable.js';

// 模拟查询结果数据
const mockQueryResult = {
  columns: [
    'id', 'name', 'email', 'phone', 'address', 'city', 'country', 
    'postal_code', 'created_at', 'updated_at', 'status', 'notes'
  ],
  rows: [
    ['1', '<PERSON>', '<EMAIL>', '******-0123', '123 Main St', 'New York', 'USA', '10001', '2023-01-15 10:30:00', '2023-12-01 14:20:00', 'active', 'VIP customer'],
    ['2', '<PERSON>', '<EMAIL>', '******-0456', '456 Oak Avenue', 'Los Angeles', 'USA', '90210', '2023-02-20 09:15:00', '2023-11-28 16:45:00', 'active', 'Regular customer'],
    ['3', '<PERSON>', '<EMAIL>', '******-0789', '789 Pine Street', 'Chicago', 'USA', '60601', '2023-03-10 11:45:00', '2023-11-25 13:30:00', 'inactive', 'Former employee'],
    ['4', 'Alice <PERSON>', '<EMAIL>', '******-0321', '321 Elm Drive', 'Houston', 'USA', '77001', '2023-04-05 08:20:00', '2023-11-20 10:15:00', 'active', 'New customer'],
    ['5', 'Charlie Wilson', '<EMAIL>', '******-0654', '654 Maple Lane', 'Phoenix', 'USA', '85001', '2023-05-12 15:10:00', '2023-11-15 12:00:00', 'pending', 'Verification needed'],
    ['6', 'Diana Davis', '<EMAIL>', '******-0987', '987 Cedar Court', 'Philadelphia', 'USA', '19101', '2023-06-18 13:25:00', '2023-11-10 09:40:00', 'active', 'Premium member'],
    ['7', 'Edward Miller', '<EMAIL>', '******-0147', '147 Birch Boulevard', 'San Antonio', 'USA', '78201', '2023-07-22 16:50:00', '2023-11-05 11:20:00', 'active', 'Long-term client'],
    ['8', 'Fiona Garcia', '<EMAIL>', '******-0258', '258 Spruce Street', 'San Diego', 'USA', '92101', '2023-08-30 12:35:00', '2023-11-01 14:55:00', 'inactive', 'Account suspended'],
  ]
};

const TableDemo = () => {
  const [selectedRow, setSelectedRow] = useState(0);
  const [selectedColumn, setSelectedColumn] = useState(0);
  const [focused, setFocused] = useState(true);

  useInput((input, key) => {
    if (key.ctrl && input === 'c') {
      process.exit(0);
    }

    if (key.upArrow || input === 'k') {
      setSelectedRow(prev => Math.max(0, prev - 1));
    } else if (key.downArrow || input === 'j') {
      setSelectedRow(prev => Math.min(mockQueryResult.rows.length - 1, prev + 1));
    } else if (key.leftArrow || input === 'h') {
      setSelectedColumn(prev => {
        const newCol = prev - 1;
        return newCol < 0 ? mockQueryResult.columns.length - 1 : newCol;
      });
    } else if (key.rightArrow || input === 'l') {
      setSelectedColumn(prev => {
        const newCol = prev + 1;
        return newCol >= mockQueryResult.columns.length ? 0 : newCol;
      });
    } else if (input === 'f') {
      setFocused(prev => !prev);
    }
  });

  return (
    <Box flexDirection="column" padding={1}>
      <Box marginBottom={1}>
        <Text bold color="cyan">
          🧪 表格功能演示
        </Text>
      </Box>
      
      <Box marginBottom={1}>
        <Text color="yellow">
          使用 ↑↓jk 导航行，←→hl 导航列，f 切换焦点，Ctrl+C 退出
        </Text>
      </Box>

      <ResultTable
        result={mockQueryResult}
        selectedRow={selectedRow}
        selectedColumn={selectedColumn}
        focused={focused}
        onViewColumn={() => {}}
      />

      <Box marginTop={1}>
        <Text color="gray">
          当前位置: 行 {selectedRow + 1}/{mockQueryResult.rows.length}, 
          列 {selectedColumn + 1}/{mockQueryResult.columns.length} ({mockQueryResult.columns[selectedColumn]})
        </Text>
      </Box>

      <Box marginTop={1}>
        <Text color="gray" italic>
          💡 调整终端窗口大小来测试响应式显示功能
        </Text>
      </Box>
    </Box>
  );
};

console.log('🚀 启动表格功能演示...');
console.log('📏 请调整终端窗口大小来测试响应式功能');
console.log('');

render(<TableDemo />);
