import { createMetadata } from './dist/database/metadata.js';

// 测试读取PostgreSQL表的列注释
async function testPostgreSQLComments() {
  console.log('🧪 测试 PostgreSQL 列注释读取');
  console.log('================================');

  try {
    // 使用配置文件中的数据库配置
    const config = {
      driver: 'postgresql',
      host: '127.0.0.1',
      port: 5432,
      user: 'postgres',
      password: 'postgres',
      db: 'wms',
      charset: 'utf8mb4'
    };

    const metadata = await createMetadata(config);

    // 获取表列表
    const tables = await metadata.getTables();
    console.log('📋 找到表:', tables.slice(0, 5).join(', '), tables.length > 5 ? '...' : '');

    // 测试特定表的列注释
    if (tables.includes('wms_gps_records')) {
      console.log('\n📋 测试 wms_gps_records 表的列信息:');
      const tableInfo = await metadata.getTableInfo('wms_gps_records');
      
      tableInfo.columns.forEach(col => {
        console.log(`- ${col.name}: ${col.type}${col.nullable ? '' : ' NOT NULL'}${col.comment ? ` -- ${col.comment}` : ''}`);
      });
    } else {
      // 测试第一个表
      console.log(`\n📋 测试 ${tables[0]} 表的列信息:`);
      const tableInfo = await metadata.getTableInfo(tables[0]);
      
      tableInfo.columns.forEach(col => {
        console.log(`- ${col.name}: ${col.type}${col.nullable ? '' : ' NOT NULL'}${col.comment ? ` -- ${col.comment}` : ''}`);
      });
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testPostgreSQLComments();