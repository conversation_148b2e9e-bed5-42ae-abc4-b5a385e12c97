import { PostgreSQLGenerator } from './dist/migration/generator.js';

console.log('🎯 演示 wms_gps_records 表的列注释生成');
console.log('===========================================');

const generator = new PostgreSQLGenerator();

// 创建 wms_gps_records 表结构，包含用户需要的注释
const wmsGpsRecordsTable = {
  name: 'wms_gps_records',
  columns: [
    {
      name: 'id',
      type: 'CHARACTER VARYING(255)',
      nullable: false
    },
    {
      name: 'created_at',
      type: 'timestamp with time zone',
      nullable: false,
      comment: '创建时间'
    },
    {
      name: 'updated_at',
      type: 'timestamp with time zone',
      nullable: false,
      comment: '更新时间'
    },
    {
      name: 'created_by',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '创建人'
    },
    {
      name: 'updated_by',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '更新人'
    },
    {
      name: 'remark',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '备注'
    },
    {
      name: 'code',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '设备编码'
    },
    {
      name: 'location',
      type: 'geometry(POINT, 4326)',
      nullable: true,
      comment: '空间点位'
    },
    {
      name: 'longitude',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '经度'
    },
    {
      name: 'latitude',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '纬度'
    },
    {
      name: 'altitude',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '海拔'
    }
  ],
  indexes: [],
  primaryKey: ['id']
};

console.log('📋 完整的 CREATE TABLE 语句（包含所有列注释）:');
console.log('');

const createTableOp = generator.generateCreateTable(wmsGpsRecordsTable);
console.log(createTableOp.sql);

console.log('');
console.log('🎉 生成的SQL包含了所有必需的列注释！');
console.log('');
console.log('💡 注意事项:');
console.log('1. 注释会在CREATE TABLE语句之后自动生成');
console.log('2. 只有包含comment字段的列才会生成注释语句');
console.log('3. 注释中的单引号会自动转义');
console.log('4. 支持中文注释内容');