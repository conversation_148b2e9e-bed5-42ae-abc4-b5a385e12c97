-- =====================================================
-- 数据库迁移文件
-- =====================================================
-- 生成时间: 2025-09-02 07:10:28
-- 时间戳: 20250902_151028
-- 源数据库: demo
-- 目标数据库: wms
-- 生成工具: dbtool migrate
-- =====================================================
-- 
-- 注意事项:
-- 1. 请在执行前备份目标数据库
-- 2. 建议在测试环境中先验证迁移脚本
-- 3. 执行前请检查所有 SQL 语句的正确性
-- 4. 如有数据迁移，请注意数据一致性
-- 
-- =====================================================

-- 创建表 wms_warning_rules
-- 操作类型: CREATE_TABLE
-- 表名: wms_warning_rules
CREATE TABLE "public"."wms_warning_rules" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "repository_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_type_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "operator" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" int4 NOT NULL,
  "is_active" bool NOT NULL DEFAULT true
);

COMMENT ON COLUMN "public"."wms_warning_rules"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_warning_rules"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_warning_rules"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_warning_rules"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_warning_rules"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_warning_rules"."repository_id" IS '仓库ID';
COMMENT ON COLUMN "public"."wms_warning_rules"."equipment_type_id" IS '装备类型';
COMMENT ON COLUMN "public"."wms_warning_rules"."operator" IS '操作符';
COMMENT ON COLUMN "public"."wms_warning_rules"."value" IS '阈值';
COMMENT ON COLUMN "public"."wms_warning_rules"."is_active" IS '是否启用';

-- 创建表 work_wx_approval_messages
-- 操作类型: CREATE_TABLE
-- 表名: work_wx_approval_messages
CREATE TABLE "public"."work_wx_approval_messages" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL DEFAULT '0',
  "to_user_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "from_user_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" int8 NOT NULL,
  "msg_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "event" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "agent_id" int8 NOT NULL,
  "third_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "sp_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "open_sp_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "open_template_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "open_sp_status" int8 NOT NULL,
  "apply_time" int8 NOT NULL,
  "apply_user_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "apply_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "apply_user_party" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "apply_user_image" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "approver_step" int8 NOT NULL
);

COMMENT ON COLUMN "public"."work_wx_approval_messages"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."remark" IS '备注';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."status" IS '状态：0 禁用、 1启用';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."to_user_name" IS '接收消息的企业微信CorpID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."from_user_name" IS '发送消息的企业微信账号ID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."create_time" IS '消息创建时间（Unix时间戳）';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."msg_type" IS '消息类型';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."event" IS '事件类型';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."agent_id" IS '企业应用ID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."third_no" IS '审批单编号';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."sp_type" IS '审批类型';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."open_sp_name" IS '审批模板名称';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."open_template_id" IS '审批模板ID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."open_sp_status" IS '审批状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_time" IS '提交审批时间（Unix时间戳）';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_name" IS '提交审批人姓名';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_id" IS '提交审批人UserID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_party" IS '提交审批人所在部门';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_image" IS '提交审批人头像URL';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."approver_step" IS '审批流程当前审批节点次序（从0开始）';

-- 创建表 work_wx_approval_nodes
-- 操作类型: CREATE_TABLE
-- 表名: work_wx_approval_nodes
CREATE TABLE "public"."work_wx_approval_nodes" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "node_status" int8 NOT NULL,
  "node_attr" int8 NOT NULL,
  "node_type" int8 NOT NULL,
  "item_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_image" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_status" int8 NOT NULL,
  "item_speech" varchar COLLATE "pg_catalog"."default",
  "item_op_time" int8 NOT NULL,
  "approval_message_id" varchar COLLATE "pg_catalog"."default" NOT NULL
);

COMMENT ON COLUMN "public"."work_wx_approval_nodes"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."node_status" IS '节点状态：1-审批中；2-已同意；3-已驳回；4-已转审';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."node_attr" IS '节点类型：1-或签；2-会签；3-单签';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."node_type" IS '审批节点类型：1-固定成员；2-标签；3-上级；4-上级的上级...以此类推';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_name" IS '审批人姓名';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_image" IS '审批人头像URL';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_user_id" IS '审批人UserID';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_status" IS '审批状态：1-审批中；2-已同意；3-已驳回；4-已转审';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_speech" IS '审批意见';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_op_time" IS '操作时间（Unix时间戳）';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."approval_message_id" IS '关联的审批消息ID';

-- 创建表 work_wx_notify_nodes
-- 操作类型: CREATE_TABLE
-- 表名: work_wx_notify_nodes
CREATE TABLE "public"."work_wx_notify_nodes" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "item_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_image" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "approval_message_id" varchar COLLATE "pg_catalog"."default" NOT NULL
);

COMMENT ON COLUMN "public"."work_wx_notify_nodes"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."item_name" IS '抄送人姓名';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."item_image" IS '抄送人头像URL';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."item_user_id" IS '抄送人UserID';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."approval_message_id" IS '关联的审批消息ID';

-- 创建表 wms_borrow_order_details
-- 操作类型: CREATE_TABLE
-- 表名: wms_borrow_order_details
CREATE TABLE "public"."wms_borrow_order_details" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "order_id" varchar COLLATE "pg_catalog"."default",
  "equipment_type_id" varchar COLLATE "pg_catalog"."default",
  "equipment_id" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default",
  "model_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "measure_unit_id" varchar COLLATE "pg_catalog"."default",
  "num" int8 NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "return_time" timestamptz(6),
  "is_return" bool NOT NULL DEFAULT false,
  "feature" jsonb
);

COMMENT ON COLUMN "public"."wms_borrow_order_details"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."order_id" IS '借用单';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."equipment_type_id" IS '设备类型';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."equipment_id" IS '设备';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."name" IS '设备名称';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."model_no" IS '规格型号';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."measure_unit_id" IS '计量单位';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."num" IS '数量';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."return_time" IS '归还时间';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."is_return" IS '是否归还';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."feature" IS '详细规格';

-- 创建表 wms_borrow_orders
-- 操作类型: CREATE_TABLE
-- 表名: wms_borrow_orders
CREATE TABLE "public"."wms_borrow_orders" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "contract_urls" varchar COLLATE "pg_catalog"."default",
  "invoice_urls" varchar COLLATE "pg_catalog"."default",
  "audit_urls" varchar COLLATE "pg_catalog"."default",
  "other_urls" varchar COLLATE "pg_catalog"."default",
  "order_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "borrow_time" timestamptz(6) NOT NULL,
  "expect_return_time" timestamptz(6) NOT NULL,
  "borrower_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_num" int8,
  "reason" text COLLATE "pg_catalog"."default"
);

COMMENT ON COLUMN "public"."wms_borrow_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_borrow_orders"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_borrow_orders"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_borrow_orders"."contract_urls" IS '合同附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."invoice_urls" IS '发票附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."audit_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."other_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."order_no" IS '借用单号';
COMMENT ON COLUMN "public"."wms_borrow_orders"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_borrow_orders"."borrow_time" IS '借用时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."expect_return_time" IS '预期归还时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."borrower_id" IS '借用人';
COMMENT ON COLUMN "public"."wms_borrow_orders"."status" IS '借用状态';
COMMENT ON COLUMN "public"."wms_borrow_orders"."equipment_num" IS '设备数量';
COMMENT ON COLUMN "public"."wms_borrow_orders"."reason" IS '事由';

-- 创建表 wms_claim_orders
-- 操作类型: CREATE_TABLE
-- 表名: wms_claim_orders
CREATE TABLE "public"."wms_claim_orders" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "contract_urls" varchar COLLATE "pg_catalog"."default",
  "invoice_urls" varchar COLLATE "pg_catalog"."default",
  "audit_urls" varchar COLLATE "pg_catalog"."default",
  "other_urls" varchar COLLATE "pg_catalog"."default",
  "order_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "claim_time" timestamptz(6) NOT NULL,
  "claim_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_num" int8,
  "reason" text COLLATE "pg_catalog"."default"
);

COMMENT ON COLUMN "public"."wms_claim_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_claim_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_claim_orders"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_claim_orders"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_claim_orders"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_claim_orders"."contract_urls" IS '合同附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."invoice_urls" IS '发票附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."audit_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."other_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."order_no" IS '领用单号';
COMMENT ON COLUMN "public"."wms_claim_orders"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_claim_orders"."claim_time" IS '领用时间';
COMMENT ON COLUMN "public"."wms_claim_orders"."claim_user_id" IS '领用人';
COMMENT ON COLUMN "public"."wms_claim_orders"."status" IS '领用状态';
COMMENT ON COLUMN "public"."wms_claim_orders"."equipment_num" IS '设备数量';
COMMENT ON COLUMN "public"."wms_claim_orders"."reason" IS '事由';

-- 创建表 wms_claim_order_details
-- 操作类型: CREATE_TABLE
-- 表名: wms_claim_order_details
CREATE TABLE "public"."wms_claim_order_details" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "order_id" varchar COLLATE "pg_catalog"."default",
  "equipment_type_id" varchar COLLATE "pg_catalog"."default",
  "equipment_id" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default",
  "model_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "measure_unit_id" varchar COLLATE "pg_catalog"."default",
  "num" int8 NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "feature" jsonb
);

COMMENT ON COLUMN "public"."wms_claim_order_details"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_claim_order_details"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_claim_order_details"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_claim_order_details"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_claim_order_details"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_claim_order_details"."order_id" IS '领用单';
COMMENT ON COLUMN "public"."wms_claim_order_details"."equipment_type_id" IS '设备类型';
COMMENT ON COLUMN "public"."wms_claim_order_details"."equipment_id" IS '设备';
COMMENT ON COLUMN "public"."wms_claim_order_details"."name" IS '设备名称';
COMMENT ON COLUMN "public"."wms_claim_order_details"."model_no" IS '规格型号';
COMMENT ON COLUMN "public"."wms_claim_order_details"."measure_unit_id" IS '计量单位';
COMMENT ON COLUMN "public"."wms_claim_order_details"."num" IS '数量';
COMMENT ON COLUMN "public"."wms_claim_order_details"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_claim_order_details"."feature" IS '详细规格';

-- 创建表 wms_transfer_orders
-- 操作类型: CREATE_TABLE
-- 表名: wms_transfer_orders
CREATE TABLE "public"."wms_transfer_orders" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "contract_urls" varchar COLLATE "pg_catalog"."default",
  "invoice_urls" varchar COLLATE "pg_catalog"."default",
  "audit_urls" varchar COLLATE "pg_catalog"."default",
  "other_urls" varchar COLLATE "pg_catalog"."default",
  "order_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_num" int8,
  "from_repository_id" varchar COLLATE "pg_catalog"."default",
  "to_repository_id" varchar COLLATE "pg_catalog"."default",
  "status" varchar COLLATE "pg_catalog"."default",
  "transfer_time" timestamptz(6)
);

COMMENT ON COLUMN "public"."wms_transfer_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_transfer_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_transfer_orders"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_transfer_orders"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_transfer_orders"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_transfer_orders"."contract_urls" IS '合同附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."invoice_urls" IS '发票附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."audit_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."other_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."order_no" IS '调拨单号';
COMMENT ON COLUMN "public"."wms_transfer_orders"."equipment_num" IS '装备数量';
COMMENT ON COLUMN "public"."wms_transfer_orders"."from_repository_id" IS '调出仓库';
COMMENT ON COLUMN "public"."wms_transfer_orders"."to_repository_id" IS '调入仓库';
COMMENT ON COLUMN "public"."wms_transfer_orders"."status" IS '调拨状态';
COMMENT ON COLUMN "public"."wms_transfer_orders"."transfer_time" IS '调拨时间';

-- 创建表 wms_transfer_order_details
-- 操作类型: CREATE_TABLE
-- 表名: wms_transfer_order_details
CREATE TABLE "public"."wms_transfer_order_details" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL PRIMARY KEY,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "order_id" varchar COLLATE "pg_catalog"."default",
  "material_id" varchar COLLATE "pg_catalog"."default",
  "material_name" varchar COLLATE "pg_catalog"."default",
  "code" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_id" varchar COLLATE "pg_catalog"."default",
  "equipment_type_id" varchar COLLATE "pg_catalog"."default",
  "feature" jsonb,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "repository_area_id" varchar COLLATE "pg_catalog"."default",
  "repository_position_id" varchar COLLATE "pg_catalog"."default",
  "owner_id" varchar COLLATE "pg_catalog"."default",
  "model_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "measure_unit_id" varchar COLLATE "pg_catalog"."default",
  "num" int8 NOT NULL,
  "transfer_reason" text COLLATE "pg_catalog"."default",
  "transfer_time" timestamptz(6)
);

COMMENT ON COLUMN "public"."wms_transfer_order_details"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."order_id" IS '调拨单';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."material_id" IS '物料';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."material_name" IS '物料名称';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."code" IS '编码';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."equipment_id" IS '装备';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."equipment_type_id" IS '装备类型';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."feature" IS '详细规格';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."repository_area_id" IS '库区';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."repository_position_id" IS '库位';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."owner_id" IS '归属人';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."model_no" IS '规格型号';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."measure_unit_id" IS '计量单位';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."num" IS '调拨数量';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."transfer_reason" IS '调拨原因';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."transfer_time" IS '调拨时间';

-- 删除表 wms_discard_orders 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: source
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "source";

-- 删除表 wms_discard_orders 中的字段 source_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: source_id
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "source_id";

-- 删除表 wms_discard_orders 中的字段 workflow_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: workflow_id
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "workflow_id";

-- 删除表 wms_discard_orders 中的字段 fire_station_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: fire_station_id
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "fire_station_id";

-- 在表 wms_discard_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_orders
-- 字段名: status
ALTER TABLE "public"."wms_discard_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default" NOT NULL;
COMMENT ON COLUMN "public"."wms_discard_orders"."status" IS '报废状态';

-- 在表 wms_discard_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_discard_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_discard_orders"."equipment_num" IS '设备数量';

-- 在表 wms_equipment_type_property_options 上创建索引 wmsequipmenttypepropertyoption_equipment_type_property_id_code
-- 操作类型: ADD_INDEX
-- 表名: wms_equipment_type_property_options
-- 索引名: wmsequipmenttypepropertyoption_equipment_type_property_id_code
-- CREATE UNIQUE INDEX "wmsequipmenttypepropertyoption_equipment_type_property_id_code" ON "public"."wms_equipment_type_property_options" ("code", "equipment_type_property_id");

-- 删除索引 wmsequipmenttypepropertyoption_equipment_type_property_id_code
-- 操作类型: DROP_INDEX
-- 表名: wms_equipment_type_property_options_bak
-- 索引名: wmsequipmenttypepropertyoption_equipment_type_property_id_code
-- DROP INDEX "public"."wmsequipmenttypepropertyoption_equipment_type_property_id_code";

-- 在表 wms_equipment_types 上创建索引 wmsequipmenttype_code
-- 操作类型: ADD_INDEX
-- 表名: wms_equipment_types
-- 索引名: wmsequipmenttype_code
-- CREATE UNIQUE INDEX "wmsequipmenttype_code" ON "public"."wms_equipment_types" ("code");

-- 删除索引 wmsequipmenttype_code
-- 操作类型: DROP_INDEX
-- 表名: wms_equipment_types_bak
-- 索引名: wmsequipmenttype_code
-- DROP INDEX "public"."wmsequipmenttype_code";

-- 删除表 wms_maintain_orders 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: source
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "source";

-- 删除表 wms_maintain_orders 中的字段 source_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: source_id
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "source_id";

-- 删除表 wms_maintain_orders 中的字段 workflow_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: workflow_id
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "workflow_id";

-- 删除表 wms_maintain_orders 中的字段 fire_station_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: fire_station_id
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "fire_station_id";

-- 在表 wms_maintain_orders 中添加字段 contract_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: contract_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "contract_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."contract_urls" IS '合同附件';

-- 在表 wms_maintain_orders 中添加字段 invoice_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: invoice_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "invoice_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."invoice_urls" IS '发票附件';

-- 在表 wms_maintain_orders 中添加字段 audit_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: audit_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "audit_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."audit_urls" IS '会审凭证附件';

-- 在表 wms_maintain_orders 中添加字段 other_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: other_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "other_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."other_urls" IS '会审凭证附件';

-- 在表 wms_maintain_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_maintain_orders"."equipment_num" IS '装备数量';

-- 在表 wms_maintain_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: status
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."status" IS '保养状态';

-- 在表 wms_maintain_orders 中添加字段 maintain_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: maintain_time
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "maintain_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_maintain_orders"."maintain_time" IS '保养时间';

-- 修改表 wms_learning_coursewares 中的字段 time_length
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_coursewares
-- 字段名: time_length
ALTER TABLE "public"."wms_learning_coursewares" ALTER COLUMN "time_length" TYPE int8;

-- 在表 wms_repair_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."order_id" IS '维修单';

-- 在表 wms_repair_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."material_id" IS '物料';

-- 在表 wms_repair_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."material_name" IS '物料名称';

-- 在表 wms_repair_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_repair_order_details"."feature" IS '详细规格';

-- 在表 wms_repair_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."owner_id" IS '归属人';

-- 在表 wms_repair_order_details 中添加字段 measure_unit_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: measure_unit_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "measure_unit_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."measure_unit_id" IS '计量单位';

-- 在表 wms_repair_order_details 中添加字段 num
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: num
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "num" int8 NOT NULL;
COMMENT ON COLUMN "public"."wms_repair_order_details"."num" IS '维修数量';

-- 在表 wms_repair_order_details 中添加字段 repair_type
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: repair_type
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "repair_type" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."repair_type" IS '维修类型';

-- 在表 wms_repair_order_details 中添加字段 repair_reason
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: repair_reason
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "repair_reason" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."repair_reason" IS '故障描述';

-- 在表 wms_repair_order_details 中添加字段 image_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: image_urls
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "image_urls" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."image_urls" IS '故障图片';

-- 在表 wms_repair_order_details 中添加字段 repair_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: repair_time
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "repair_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_repair_order_details"."repair_time" IS '维修时间';

-- 在表 wms_repair_order_details 中添加字段 complete_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: complete_time
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "complete_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_repair_order_details"."complete_time" IS '完成时间';

-- 在表 wms_repair_order_details 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: status
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "status" int4 DEFAULT '0';
COMMENT ON COLUMN "public"."wms_repair_order_details"."status" IS '状态';

-- 在表 wms_repair_order_details 中添加字段 files
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: files
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "files" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."files" IS '附件';

-- 在表 wms_repair_order_details 中添加字段 complete_extra_info
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: complete_extra_info
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "complete_extra_info" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."complete_extra_info" IS '完成的补充说明';

-- 在表 wms_repair_order_details 中添加字段 order_status
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: order_status
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "order_status" varchar COLLATE "pg_catalog"."default" DEFAULT '0'::character varying;
COMMENT ON COLUMN "public"."wms_repair_order_details"."order_status" IS '订单状态';

-- 修改表 wms_audit_plan_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_audit_plan_details
-- 字段名: num
ALTER TABLE "public"."wms_audit_plan_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_meeting_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_meeting_details
-- 字段名: num
ALTER TABLE "public"."wms_discard_meeting_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_meetings 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_meetings
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_discard_meetings" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_discard_meetings 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_meetings
-- 字段名: equipment_num
ALTER TABLE "public"."wms_discard_meetings" ALTER COLUMN "equipment_num" TYPE int8;

-- 在表 wms_rfid_readers 中添加字段 alarm_user_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_rfid_readers
-- 字段名: alarm_user_id
ALTER TABLE "public"."wms_rfid_readers" ADD COLUMN "alarm_user_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_rfid_readers"."alarm_user_id" IS '报警用户';

-- 删除表 wms_discard_order_details 中的字段 enter_repository_time
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: enter_repository_time
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "enter_repository_time";

-- 删除表 wms_discard_order_details 中的字段 order_no
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: order_no
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "order_no";

-- 删除表 wms_discard_order_details 中的字段 discard_order_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: discard_order_id
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "discard_order_id";

-- 删除表 wms_discard_order_details 中的字段 price
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: price
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "price";

-- 修改表 wms_discard_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: num
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_order_details 中的字段 reason
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: reason
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "reason" TYPE text COLLATE "pg_catalog"."default";
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "reason" DROP NOT NULL;

-- 修改表 wms_discard_order_details 中的字段 discard_time
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: discard_time
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "discard_time" DROP NOT NULL;

-- 在表 wms_discard_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."order_id" IS '报废单';

-- 在表 wms_discard_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."material_id" IS '物料';

-- 在表 wms_discard_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."material_name" IS '物料名称';

-- 在表 wms_discard_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_discard_order_details"."feature" IS '详细规格';

-- 在表 wms_discard_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."owner_id" IS '归属人';

-- 修改表 wms_discard_plan_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_plan_order_details
-- 字段名: num
ALTER TABLE "public"."wms_discard_plan_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_plan_orders 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_plan_orders
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_discard_plan_orders" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_discard_plan_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_plan_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_discard_plan_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_enter_repository_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_enter_repository_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_enter_repository_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_enter_repository_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_enter_repository_order_details
-- 字段名: num
ALTER TABLE "public"."wms_enter_repository_order_details" ALTER COLUMN "num" TYPE int8;

-- 在表 wms_enter_repository_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_enter_repository_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_enter_repository_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_enter_repository_order_details"."material_id" IS '资产id';

-- 修改表 wms_learning_course_records 中的字段 process
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_course_records
-- 字段名: process
ALTER TABLE "public"."wms_learning_course_records" ALTER COLUMN "process" TYPE int8;

-- 修改表 wms_learning_courseware_records 中的字段 process
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_courseware_records
-- 字段名: process
ALTER TABLE "public"."wms_learning_courseware_records" ALTER COLUMN "process" TYPE int8;

-- 修改表 wms_learning_plan_records 中的字段 process
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_plan_records
-- 字段名: process
ALTER TABLE "public"."wms_learning_plan_records" ALTER COLUMN "process" TYPE int8;

-- 修改表 wms_repository_screens 中的字段 equipment_type_carousel_interval
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repository_screens
-- 字段名: equipment_type_carousel_interval
ALTER TABLE "public"."wms_repository_screens" ALTER COLUMN "equipment_type_carousel_interval" TYPE int8;

-- 修改表 wms_repository_screens 中的字段 equipment_type_sub_carousel_interval
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repository_screens
-- 字段名: equipment_type_sub_carousel_interval
ALTER TABLE "public"."wms_repository_screens" ALTER COLUMN "equipment_type_sub_carousel_interval" TYPE int8;

-- 修改表 wms_repository_screens 中的字段 material_carousel_interval
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repository_screens
-- 字段名: material_carousel_interval
ALTER TABLE "public"."wms_repository_screens" ALTER COLUMN "material_carousel_interval" TYPE int8;

-- 修改表 wms_maintain_plan_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_plan_details
-- 字段名: num
ALTER TABLE "public"."wms_maintain_plan_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_maintain_plans 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_plans
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_maintain_plans" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_maintain_plans 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_plans
-- 字段名: equipment_num
ALTER TABLE "public"."wms_maintain_plans" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_material_logs 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_material_logs
-- 字段名: num
ALTER TABLE "public"."wms_material_logs" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_materials 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_materials
-- 字段名: num
ALTER TABLE "public"."wms_materials" ALTER COLUMN "num" TYPE int8;

-- 在表 wms_materials 中添加字段 finance_system_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_materials
-- 字段名: finance_system_id
ALTER TABLE "public"."wms_materials" ADD COLUMN "finance_system_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_materials"."finance_system_id" IS '财务系统id';

-- 在表 wms_materials 中添加字段 use_start_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_materials
-- 字段名: use_start_time
ALTER TABLE "public"."wms_materials" ADD COLUMN "use_start_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_materials"."use_start_time" IS '使用开始时间';

-- 在表 wms_materials 中添加字段 finance_system_no
-- 操作类型: ADD_COLUMN
-- 表名: wms_materials
-- 字段名: finance_system_no
ALTER TABLE "public"."wms_materials" ADD COLUMN "finance_system_no" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_materials"."finance_system_no" IS '财务系统编号';

-- 删除表 wms_maintain_order_details 中的字段 enter_repository_time
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: enter_repository_time
ALTER TABLE "public"."wms_maintain_order_details" DROP COLUMN "enter_repository_time";

-- 删除表 wms_maintain_order_details 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: source
ALTER TABLE "public"."wms_maintain_order_details" DROP COLUMN "source";

-- 删除表 wms_maintain_order_details 中的字段 maintain_order_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: maintain_order_id
ALTER TABLE "public"."wms_maintain_order_details" DROP COLUMN "maintain_order_id";

-- 修改表 wms_maintain_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: num
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_maintain_order_details 中的字段 reason
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: reason
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "reason" TYPE text COLLATE "pg_catalog"."default";
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "reason" DROP NOT NULL;

-- 修改表 wms_maintain_order_details 中的字段 maintain_time
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: maintain_time
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "maintain_time" DROP NOT NULL;

-- 在表 wms_maintain_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."order_id" IS '保养单';

-- 在表 wms_maintain_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."material_id" IS '物料';

-- 在表 wms_maintain_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."material_name" IS '物料名称';

-- 在表 wms_maintain_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_maintain_order_details"."feature" IS '详细规格';

-- 在表 wms_maintain_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."owner_id" IS '归属人';

-- 在表 wms_maintain_order_details 中添加字段 complete_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: complete_time
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "complete_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_maintain_order_details"."complete_time" IS '完成时间';

-- 在表 wms_maintain_order_details 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: status
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "status" int4 DEFAULT '0';
COMMENT ON COLUMN "public"."wms_maintain_order_details"."status" IS '状态';

-- 在表 wms_maintain_order_details 中添加字段 files
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: files
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "files" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."files" IS '附件';

-- 在表 wms_maintain_order_details 中添加字段 complete_extra_info
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: complete_extra_info
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "complete_extra_info" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."complete_extra_info" IS '完成的补充说明';

-- 在表 wms_maintain_order_details 中添加字段 order_status
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: order_status
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "order_status" varchar COLLATE "pg_catalog"."default" DEFAULT '0'::character varying;
COMMENT ON COLUMN "public"."wms_maintain_order_details"."order_status" IS '订单状态';

-- 修改表 wms_out_repository_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_out_repository_order_details
-- 字段名: num
ALTER TABLE "public"."wms_out_repository_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_out_repository_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_out_repository_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_out_repository_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_purchase_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_purchase_order_details
-- 字段名: num
ALTER TABLE "public"."wms_purchase_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_purchase_orders 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_purchase_orders
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_purchase_orders" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_purchase_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_purchase_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_purchase_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_repair_settlement_order_workfee_details 中的字段 hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_order_workfee_details
-- 字段名: hours
ALTER TABLE "public"."wms_repair_settlement_order_workfee_details" ALTER COLUMN "hours" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 work_hours_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: work_hours_standard
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "work_hours_standard" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 execute_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: execute_standard
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "execute_standard" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 total_work_hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: total_work_hours
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "total_work_hours" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 is_old_confirm_and_recover
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: is_old_confirm_and_recover
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "is_old_confirm_and_recover" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 is_old_confirm_and_giveup
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: is_old_confirm_and_giveup
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "is_old_confirm_and_giveup" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 is_none_old
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: is_none_old
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "is_none_old" TYPE int8;

-- 删除表 wms_return_order_details 中的字段 enter_repository_time
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: enter_repository_time
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "enter_repository_time";

-- 删除表 wms_return_order_details 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: source
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "source";

-- 删除表 wms_return_order_details 中的字段 return_order_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: return_order_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "return_order_id";

-- 删除表 wms_return_order_details 中的字段 repository_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: repository_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "repository_id";

-- 删除表 wms_return_order_details 中的字段 repository_area_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: repository_area_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "repository_area_id";

-- 删除表 wms_return_order_details 中的字段 repository_position_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: repository_position_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "repository_position_id";

-- 修改表 wms_return_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_return_order_details
-- 字段名: num
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_return_order_details 中的字段 reason
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_return_order_details
-- 字段名: reason
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "reason" TYPE text COLLATE "pg_catalog"."default";
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "reason" DROP NOT NULL;

-- 修改表 wms_return_order_details 中的字段 return_time
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_return_order_details
-- 字段名: return_time
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "return_time" DROP NOT NULL;

-- 在表 wms_return_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."order_id" IS '退还单';

-- 在表 wms_return_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."material_id" IS '物料';

-- 在表 wms_return_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."material_name" IS '物料名称';

-- 在表 wms_return_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_return_order_details"."feature" IS '详细规格';

-- 在表 wms_return_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."owner_id" IS '归属人';

-- 在表 wms_return_order_details 中添加字段 to_repository_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: to_repository_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "to_repository_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."to_repository_id" IS '仓库';

-- 在表 wms_return_order_details 中添加字段 to_repository_area_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: to_repository_area_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "to_repository_area_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."to_repository_area_id" IS '库区';

-- 在表 wms_return_order_details 中添加字段 to_repository_position_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: to_repository_position_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "to_repository_position_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."to_repository_position_id" IS '库位';

-- 删除表 wms_return_orders 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: source
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "source";

-- 删除表 wms_return_orders 中的字段 source_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: source_id
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "source_id";

-- 删除表 wms_return_orders 中的字段 workflow_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: workflow_id
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "workflow_id";

-- 删除表 wms_return_orders 中的字段 fire_station_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: fire_station_id
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "fire_station_id";

-- 在表 wms_return_orders 中添加字段 contract_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: contract_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "contract_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."contract_urls" IS '合同附件';

-- 在表 wms_return_orders 中添加字段 invoice_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: invoice_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "invoice_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."invoice_urls" IS '发票附件';

-- 在表 wms_return_orders 中添加字段 audit_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: audit_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "audit_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."audit_urls" IS '会审凭证附件';

-- 在表 wms_return_orders 中添加字段 other_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: other_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "other_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."other_urls" IS '会审凭证附件';

-- 在表 wms_return_orders 中添加字段 repository_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: repository_id
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "repository_id" varchar COLLATE "pg_catalog"."default" NOT NULL;
COMMENT ON COLUMN "public"."wms_return_orders"."repository_id" IS '仓库id';

-- 在表 wms_return_orders 中添加字段 reason
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: reason
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "reason" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."reason" IS '退还原因';

-- 在表 wms_return_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_return_orders"."equipment_num" IS '装备数量';

-- 在表 wms_return_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: status
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."status" IS '退还状态';

-- 在表 wms_return_orders 中添加字段 return_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: return_time
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "return_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_return_orders"."return_time" IS '退还时间';

-- 在表 wms_repair_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_repair_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_repair_orders"."equipment_num" IS '装备数量';

-- 在表 wms_repair_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_orders
-- 字段名: status
ALTER TABLE "public"."wms_repair_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_orders"."status" IS '维修状态';

-- 在表 wms_repair_orders 中添加字段 repair_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_orders
-- 字段名: repair_time
ALTER TABLE "public"."wms_repair_orders" ADD COLUMN "repair_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_repair_orders"."repair_time" IS '维修时间';

-- 修改表 wms_vehicle_repair_order_materialfee_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_order_materialfee_details
-- 字段名: num
ALTER TABLE "public"."wms_vehicle_repair_order_materialfee_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_vehicle_repair_order_workfee_details 中的字段 hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_order_workfee_details
-- 字段名: hours
ALTER TABLE "public"."wms_vehicle_repair_order_workfee_details" ALTER COLUMN "hours" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 work_hours_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: work_hours_standard
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "work_hours_standard" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 execute_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: execute_standard
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "execute_standard" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 total_work_hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: total_work_hours
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "total_work_hours" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 total_material_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: total_material_num
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "total_material_num" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 is_old_confirm_and_recover
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: is_old_confirm_and_recover
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "is_old_confirm_and_recover" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 is_old_confirm_and_giveup
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: is_old_confirm_and_giveup
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "is_old_confirm_and_giveup" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 is_none_old
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: is_none_old
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "is_none_old" TYPE int8;


-- -- 向表 sys_resources 插入 87 条数据
-- -- 操作类型: INSERT_DATA
-- -- 表名: sys_resources
-- INSERT INTO "public"."sys_resources" ("id", "created_at", "updated_at", "created_by", "updated_by", "sort", "name", "code", "type", "depends_id", "path", "method", "actions", "icon", "display_name", "parent_id", "status", "remark", "is_link", "is_show", "is_keep_alive", "depend_ids") VALUES
--   ('0fd6673a-61c3-4d1a-84b7-4a4da2331af5', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '系统管理', '系统管理_9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 1, NULL, NULL, NULL, NULL, NULL),
--   ('5f223b23-0545-4d64-837e-5f339428f0f5', Sun Dec 17 2023 23:17:03 GMT+0800 (China Standard Time), Thu Dec 28 2023 20:21:43 GMT+0800 (China Standard Time), NULL, '2e6c5200-358f-11ec-981f-de2269351a1e', 10, '应用菜单', 'menu:system:application:menu', 'MENU', NULL, '/@/views/system/sysMenu/Index.vue', NULL, NULL, NULL, NULL, '4fa13862-308b-4212-9daf-45c4d3338e0d', 1, '', 0, 0, 0, NULL),
--   ('2e3c4cf5-3f27-47ba-ac1e-8571c00c3e66', Wed Dec 13 2023 15:55:55 GMT+0800 (China Standard Time), Thu Dec 28 2023 22:29:58 GMT+0800 (China Standard Time), NULL, '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '模块列表', 'menu:module:list', 'MENU', NULL, '/@/views/system/sysModule/Index.vue', NULL, NULL, '', NULL, '4629aef5-23e3-45bb-8ec9-27d779db1dd1', 1, NULL, 0, 0, 0, 85b60422-ee69-4834-b77d-abe11713bc15),
--   ('03b37f88-295b-4c28-bb14-efa16e7773d6', Fri Dec 15 2023 23:26:50 GMT+0800 (China Standard Time), Fri Dec 15 2023 23:26:50 GMT+0800 (China Standard Time), NULL, NULL, 0, '323223', '232323', 'MENU', NULL, NULL, NULL, NULL, NULL, NULL, '3e0541fe-b8a8-4de3-9c81-18b7bb24c510', 1, '', 0, 0, 0, NULL),
--   ('dfabf267-0766-48c7-a117-2eae084e588b', Fri Dec 15 2023 11:34:33 GMT+0800 (China Standard Time), Wed Dec 20 2023 14:24:55 GMT+0800 (China Standard Time), NULL, NULL, 0, '资源列表', 'menu:resource:list', 'MENU', NULL, '/@/views/system/sysResource/Index.vue', NULL, NULL, NULL, NULL, '4629aef5-23e3-45bb-8ec9-27d779db1dd1', 1, NULL, 0, NULL, 0, NULL),
--   ('0b2bb68c-3596-4b0f-9835-a1f2d115329c', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '用户管理', '用户管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('3e0541fe-b8a8-4de3-9c81-18b7bb24c510', Fri Dec 15 2023 12:14:55 GMT+0800 (China Standard Time), Fri Dec 15 2023 23:33:39 GMT+0800 (China Standard Time), NULL, NULL, 0, '建筑管理', '322323', 'MENU', NULL, NULL, NULL, NULL, NULL, NULL, '16548c37-cf75-4be5-b38d-81975881009b', 1, '', 0, 0, 0, NULL),
--   ('16548c37-cf75-4be5-b38d-81975881009b', Fri Dec 15 2023 12:14:42 GMT+0800 (China Standard Time), Fri Dec 15 2023 23:34:29 GMT+0800 (China Standard Time), NULL, NULL, 0, '仓库管理', '2323', 'MENU', NULL, NULL, NULL, NULL, NULL, NULL, '071631e0-44e2-4eb6-9509-3872987b39fc', 1, '', 0, 0, 0, NULL),
--   ('071631e0-44e2-4eb6-9509-3872987b39fc', Fri Dec 15 2023 11:32:14 GMT+0800 (China Standard Time), Fri Dec 15 2023 11:32:14 GMT+0800 (China Standard Time), NULL, NULL, 0, '菜单', 'mobile-menu', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '730755a5-9801-49df-be80-677b7a9970f4', 1, '', 0, 0, 0, NULL),
--   ('9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', Tue Dec 19 2023 10:41:38 GMT+0800 (China Standard Time), Tue Dec 19 2023 10:41:48 GMT+0800 (China Standard Time), NULL, NULL, 100, 'API', 'dir:api', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '', 0, 0, 0, NULL),
--   ('d15b8661-20b7-42c8-a1f7-7124825294da', Tue Dec 19 2023 10:54:44 GMT+0800 (China Standard Time), Tue Dec 19 2023 10:54:44 GMT+0800 (China Standard Time), NULL, NULL, 0, '系统管理', 'system', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, 'f3963a65-7142-49f1-bf71-70078a794814', 1, '', 0, 0, 0, NULL),
--   ('7873ff0a-829d-4f7f-a229-79c23342db5b', Tue Dec 19 2023 11:38:05 GMT+0800 (China Standard Time), Tue Dec 19 2023 11:38:05 GMT+0800 (China Standard Time), NULL, NULL, 0, '其他设置', 'system:other', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, 'd15b8661-20b7-42c8-a1f7-7124825294da', 1, '', 0, 0, 0, NULL),
--   ('e70d55a3-c54d-4e82-a8c5-4903c5edbe1e', Tue Dec 19 2023 11:39:26 GMT+0800 (China Standard Time), Tue Dec 19 2023 11:40:01 GMT+0800 (China Standard Time), NULL, NULL, 0, '字典列表', 'system:dictionary', 'MENU', NULL, '/@/views/system/sysDictionary/Index.vue', NULL, NULL, NULL, NULL, '7873ff0a-829d-4f7f-a229-79c23342db5b', 1, '', 0, 0, 0, NULL),
--   ('0807febd-8d4b-460c-8785-a7b31a6f427a', Thu Dec 21 2023 18:19:26 GMT+0800 (China Standard Time), Sun Dec 24 2023 12:17:39 GMT+0800 (China Standard Time), NULL, NULL, 0, '应用角色', 'system:team:application:role', 'MENU', NULL, '/@/views/system/sysRole/Index.vue', NULL, NULL, NULL, NULL, '57f4af00-39b1-4076-9e09-d08e4e81487e', 1, '', 0, NULL, 0, NULL),
--   ('bc178f7b-07d2-45b2-8a94-db82d4dd5e01', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '应用管理', '应用管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('2a7431ff-12b0-4a36-88a9-f8c9cd4033e3', Sun Dec 24 2023 12:17:09 GMT+0800 (China Standard Time), Sun Dec 24 2023 12:22:10 GMT+0800 (China Standard Time), NULL, NULL, 0, '应用成员', 'system:team:application:user', 'MENU', NULL, '/@/views/system/sysUser/Index.vue', NULL, NULL, NULL, NULL, '57f4af00-39b1-4076-9e09-d08e4e81487e', 1, '', NULL, NULL, NULL, NULL),
--   ('67750607-546e-4e8b-9cd8-26bf4c8bf0b3', Fri Dec 22 2023 10:38:01 GMT+0800 (China Standard Time), Wed Dec 27 2023 11:15:04 GMT+0800 (China Standard Time), NULL, NULL, 0, '添加', 'ADD', 'BUTTON', NULL, NULL, NULL, NULL, NULL, NULL, '5f223b23-0545-4d64-837e-5f339428f0f5', 1, '', 0, NULL, 0, 6e0ff37e-404d-4975-b201-4fc3c4b1c379),
--   ('57f4af00-39b1-4076-9e09-d08e4e81487e', Wed Dec 20 2023 21:05:04 GMT+0800 (China Standard Time), Wed Dec 20 2023 22:03:36 GMT+0800 (China Standard Time), NULL, NULL, 0, '团队应用', 'system:team:application', 'MENU', NULL, '/@/views/system/sysTeamApplication/Index.vue', NULL, NULL, NULL, NULL, 'fa15efc3-e040-4c71-9a6d-acf14eeffbb0', 1, '', 0, NULL, 0, NULL),
--   ('fa15efc3-e040-4c71-9a6d-acf14eeffbb0', Wed Dec 20 2023 18:12:45 GMT+0800 (China Standard Time), Wed Dec 20 2023 22:03:43 GMT+0800 (China Standard Time), NULL, NULL, 0, '团队列表', 'system:team:list', 'MENU', NULL, '/@/views/system/sysTeam/Index.vue', NULL, NULL, NULL, NULL, '4629aef5-23e3-45bb-8ec9-27d779db1dd1', 1, '', 0, NULL, 0, NULL),
--   ('4629aef5-23e3-45bb-8ec9-27d779db1dd1', Tue Dec 19 2023 10:55:37 GMT+0800 (China Standard Time), Wed Dec 20 2023 22:05:00 GMT+0800 (China Standard Time), NULL, NULL, 0, '应用管理', 'dir:system:application', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, 'd15b8661-20b7-42c8-a1f7-7124825294da', 1, '', 0, 0, 0, NULL),
--   ('f3963a65-7142-49f1-bf71-70078a794814', Tue Dec 19 2023 10:42:24 GMT+0800 (China Standard Time), Thu Dec 21 2023 11:47:33 GMT+0800 (China Standard Time), NULL, NULL, 100, 'PC菜单', 'dir:pc:menu', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '', 1, '', 0, 0, 0, e4d64608-e8d2-4d29-a99d-67b08f7cc8ff),
--   ('745a4a21-268c-4fc7-a802-41978f03badc', Fri Dec 22 2023 19:35:15 GMT+0800 (China Standard Time), Wed Dec 27 2023 11:15:25 GMT+0800 (China Standard Time), NULL, NULL, 0, '删除', 'DELETE', 'BUTTON', NULL, NULL, NULL, NULL, NULL, NULL, '5f223b23-0545-4d64-837e-5f339428f0f5', 1, '', NULL, NULL, NULL, 2e1c70d2-8b19-4f3c-acf4-81f4b670eeb9),
--   ('730755a5-9801-49df-be80-677b7a9970f4', Fri Dec 15 2023 11:31:52 GMT+0800 (China Standard Time), Wed Jan 10 2024 16:07:47 GMT+0800 (China Standard Time), NULL, 'test222', 1000, '移动端菜单', 'mobile-root', 'FOLDER', NULL, NULL, NULL, NULL, 'ant-design:appstore-outlined', NULL, '', 1, '', 0, 0, 0, NULL),
--   ('4fa13862-308b-4212-9daf-45c4d3338e0d', Wed Dec 13 2023 15:55:55 GMT+0800 (China Standard Time), Wed Dec 20 2023 14:22:49 GMT+0800 (China Standard Time), NULL, NULL, 0, '应用列表', 'menu:application:list', 'MENU', NULL, '/@/views/system/sysApplication/Index.vue', NULL, NULL, NULL, NULL, '4629aef5-23e3-45bb-8ec9-27d779db1dd1', 1, NULL, 0, 0, 0, NULL),
--   ('2d87aee1-07f5-4f26-bac2-2ea8de8f63fe', Wed Dec 20 2023 22:03:20 GMT+0800 (China Standard Time), Thu Dec 21 2023 17:15:41 GMT+0800 (China Standard Time), NULL, NULL, 0, '应用组织', 'system:team:organization', 'MENU', NULL, '/@/views/system/sysOrganization/Index.vue', NULL, NULL, NULL, NULL, '57f4af00-39b1-4076-9e09-d08e4e81487e', 1, '', 0, NULL, 0, NULL),
--   ('8a0de75c-be03-46c5-ac96-f330bb83a679', Tue Dec 19 2023 15:01:23 GMT+0800 (China Standard Time), Thu Dec 21 2023 22:44:25 GMT+0800 (China Standard Time), NULL, NULL, 0, '字典详情', 'system:dictionary:detail', 'MENU', NULL, '/@/views/system/sysDictionaryDetail/Index.vue', NULL, NULL, NULL, NULL, 'e70d55a3-c54d-4e82-a8c5-4903c5edbe1e', 1, '', 0, 0, 0, NULL),
--   ('9d2851b1-e139-45c8-8ebe-8ec690da385f', Fri Dec 22 2023 19:35:05 GMT+0800 (China Standard Time), Fri Dec 22 2023 19:35:21 GMT+0800 (China Standard Time), NULL, NULL, 0, '编辑', 'EDIT', 'BUTTON', NULL, NULL, NULL, NULL, NULL, NULL, '5f223b23-0545-4d64-837e-5f339428f0f5', 1, '', NULL, NULL, NULL, NULL),
--   ('989d97f7-fcb3-464e-9d9e-c84be64c2c31', Thu Dec 28 2023 20:22:43 GMT+0800 (China Standard Time), Thu Dec 28 2023 22:18:08 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '项目菜单', 'system:application:project:menu', 'MENU', NULL, '/@/views/system/sysProjectMenu/Index.vue', NULL, NULL, NULL, NULL, '4fa13862-308b-4212-9daf-45c4d3338e0d', 1, '', NULL, NULL, NULL, NULL),
--   ('34dc2eea-b224-4c79-97b4-49e869b16645', Fri Dec 29 2023 11:55:20 GMT+0800 (China Standard Time), Fri Dec 29 2023 11:55:44 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '项目角色', 'system:teamp:application:projectRole', 'MENU', NULL, '/@/views/system/sysProjectRole/Index.vue', NULL, NULL, '', NULL, '57f4af00-39b1-4076-9e09-d08e4e81487e', 1, '', NULL, NULL, NULL, NULL),
--   ('81060968-c152-4680-bb90-04ef57f25811', Thu Dec 28 2023 17:36:47 GMT+0800 (China Standard Time), Fri Dec 29 2023 11:55:54 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '项目列表', 'system:team:application:project', 'MENU', NULL, '/@/views/system/sysProject/Index.vue', NULL, NULL, '', NULL, '57f4af00-39b1-4076-9e09-d08e4e81487e', 1, '', NULL, NULL, NULL, NULL),
--   ('8ed0577a-5b14-4288-bc34-315decc08e77', Mon Jan 08 2024 11:02:06 GMT+0800 (China Standard Time), Mon Jan 08 2024 11:04:15 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '标签管理', '123', 'MENU', NULL, '/@/views/management/tagManagement/Index.vue', NULL, NULL, '', NULL, 'e748e3dd-3e06-46dc-8ddd-375391e0d46b', 1, '', NULL, NULL, NULL, NULL),
--   ('dcdee759-15fd-4642-a634-55e919b01c58', Mon Jan 08 2024 11:37:48 GMT+0800 (China Standard Time), Mon Jan 08 2024 11:38:58 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '库位管理', '1234', 'MENU', NULL, '/@/views/management/InventoryLocationManagement/Index.vue', NULL, NULL, '', NULL, 'e748e3dd-3e06-46dc-8ddd-375391e0d46b', 1, '', NULL, NULL, NULL, NULL),
--   ('e748e3dd-3e06-46dc-8ddd-375391e0d46b', Mon Jan 08 2024 10:59:42 GMT+0800 (China Standard Time), Mon Jan 08 2024 10:59:42 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', NULL, 0, '仓库管理', '1233', 'FOLDER', NULL, NULL, NULL, NULL, '', NULL, 'd15b8661-20b7-42c8-a1f7-7124825294da', 1, '', NULL, NULL, NULL, NULL),
--   ('54372184-0f4b-45ff-b5b5-3f8f20cafc75', Mon Jan 22 2024 11:03:51 GMT+0800 (China Standard Time), Mon Jan 22 2024 11:04:14 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '审批流程配置', '222', 'MENU', NULL, '/@/views/management/approvalProcess/Index.vue', NULL, NULL, '', NULL, 'e748e3dd-3e06-46dc-8ddd-375391e0d46b', 1, '', NULL, NULL, NULL, NULL),
--   ('f4167b8a-46d6-4467-ac78-7ee873327518', Wed Jan 10 2024 17:01:27 GMT+0800 (China Standard Time), Wed Jan 10 2024 17:02:35 GMT+0800 (China Standard Time), 'test222', 'test222', 0, '装备管理', '啊dasd sad撒', 'MENU', NULL, '/@/views/management/equipmentType/Index.vue', NULL, NULL, '', NULL, 'e748e3dd-3e06-46dc-8ddd-375391e0d46b', 1, '', NULL, NULL, NULL, NULL),
--   ('f4c8ed88-f091-453c-888d-0dfaafae1d75', Mon Jan 22 2024 17:45:48 GMT+0800 (China Standard Time), Mon Jan 22 2024 17:46:12 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '测量单位管理', '1', 'MENU', NULL, '/@/views/management/measurementUnitManagement/Index.vue', NULL, NULL, '', NULL, 'e748e3dd-3e06-46dc-8ddd-375391e0d46b', 1, '', NULL, NULL, NULL, NULL),
--   ('accd4fda-df4b-48e2-b6ee-e2a908343696', Tue Jan 16 2024 18:20:49 GMT+0800 (China Standard Time), Tue Jan 16 2024 18:39:48 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '界面编辑器', 'dfsffsdf', 'MENU', NULL, '/@/views/system/editor/editor/Index.vue', NULL, NULL, '', NULL, '7873ff0a-829d-4f7f-a229-79c23342db5b', 1, '', NULL, NULL, NULL, NULL),
--   ('8d35df29-d8af-4256-acdf-dfcf9c830492', Fri Jan 19 2024 11:46:53 GMT+0800 (China Standard Time), Fri Jan 19 2024 11:49:24 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', '2e6c5200-358f-11ec-981f-de2269351a1e', 0, '产品管理', '12', 'MENU', NULL, '/@/views/management/productManagement/Index.vue', NULL, NULL, '', NULL, 'e748e3dd-3e06-46dc-8ddd-375391e0d46b', 1, '', NULL, NULL, NULL, NULL),
--   ('bc03d7f1-75a2-4646-95a7-63df5662fd5a', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '项目管理', '项目管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('f0880167-c645-4b12-9959-227ae66b4333', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '地区管理', '地区管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('486e6d7d-fe2a-489e-b0cb-fb208c766db4', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '省份', '省份_f0880167-c645-4b12-9959-227ae66b4333', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, 'f0880167-c645-4b12-9959-227ae66b4333', 1, NULL, NULL, NULL, NULL, NULL),
--   ('f1676afb-57f1-47dc-8ac6-9019ba2e0623', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '资源管理', '资源管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('da27115e-8374-4b40-95c3-306e9b5dfb8a', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '团队管理', '团队管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '仓库', '仓库_9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 1, NULL, NULL, NULL, NULL, NULL),
--   ('05f9afe2-50c1-42a7-b065-099c933b5d59', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '库区管理', '库区管理_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('4b38fae2-eafd-4811-b5e3-639092c63786', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '装备', '装备_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('3b3f1156-95b6-4258-b592-9c6049985dc3', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '装备类型属性', '装备类型属性_4b38fae2-eafd-4811-b5e3-639092c63786', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '4b38fae2-eafd-4811-b5e3-639092c63786', 1, NULL, NULL, NULL, NULL, NULL),
--   ('48c61dfe-3108-4fd0-8170-d5231614b40e', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '学习课程', '学习课程_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('9de604c8-3ca6-4d19-adbb-7c60ff7a0ed8', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:06 GMT+0800 (China Standard Time), NULL, NULL, 0, '课程课件', '课程课件_48c61dfe-3108-4fd0-8170-d5231614b40e', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '48c61dfe-3108-4fd0-8170-d5231614b40e', 1, NULL, NULL, NULL, NULL, NULL),
--   ('47a4c3d6-c4bd-44e1-9324-dd8acd428cd2', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:07 GMT+0800 (China Standard Time), NULL, NULL, 0, '仓库库位', '仓库库位_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('57d89630-cb28-466a-9113-6e61a610ac3b', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:07 GMT+0800 (China Standard Time), NULL, NULL, 0, '城市', '城市_f0880167-c645-4b12-9959-227ae66b4333', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, 'f0880167-c645-4b12-9959-227ae66b4333', 1, NULL, NULL, NULL, NULL, NULL),
--   ('ce5a051b-7f36-4981-9683-e3e694da83fa', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:07 GMT+0800 (China Standard Time), NULL, NULL, 0, '角色管理', '角色管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('cpt7e2i9fp8s73aeh2t0', Tue Jun 25 2024 15:51:38 GMT+0800 (China Standard Time), Tue Jun 25 2024 15:51:38 GMT+0800 (China Standard Time), 'zhangxin', NULL, 4, '查询', 'view', 'BUTTON', NULL, '', 'GET', NULL, '', '', 'cp7gqda9fp8s73du7nmg', 1, '', NULL, NULL, NULL, NULL),
--   ('8a403835-4d1f-41d2-a711-bddfff18cb88', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:07 GMT+0800 (China Standard Time), NULL, NULL, 0, '消防站', '消防站_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('2562c06c-503b-4833-862a-f47a23dfcf79', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:07 GMT+0800 (China Standard Time), NULL, NULL, 0, '审批', '审批_9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 1, NULL, NULL, NULL, NULL, NULL),
--   ('097ba9cd-9099-4d3d-8fe1-f42b0e01f430', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:07 GMT+0800 (China Standard Time), NULL, NULL, 0, '审批分组', '审批分组_2562c06c-503b-4833-862a-f47a23dfcf79', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '2562c06c-503b-4833-862a-f47a23dfcf79', 1, NULL, NULL, NULL, NULL, NULL),
--   ('e0330686-bab4-418c-b2dc-fddd5d726e14', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:08 GMT+0800 (China Standard Time), NULL, NULL, 0, '组织管理', '组织管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('dfad7f3d-6c44-4706-9573-a83598a2dd42', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:08 GMT+0800 (China Standard Time), NULL, NULL, 0, '审批记录', '审批记录_2562c06c-503b-4833-862a-f47a23dfcf79', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '2562c06c-503b-4833-862a-f47a23dfcf79', 1, NULL, NULL, NULL, NULL, NULL),
--   ('cfbe2c5f-f7e1-4041-8a81-b178d990abb0', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:08 GMT+0800 (China Standard Time), NULL, NULL, 0, '公司管理', '公司管理_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('74052fc0-4845-4f4b-99d7-4f2cd65a451d', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:08 GMT+0800 (China Standard Time), NULL, NULL, 0, '装备类型', '装备类型_4b38fae2-eafd-4811-b5e3-639092c63786', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '4b38fae2-eafd-4811-b5e3-639092c63786', 1, NULL, NULL, NULL, NULL, NULL),
--   ('2c561c8a-6022-4069-8509-d0d184baa461', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '用户组织', '用户组织_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('05c7f817-ce0b-4d79-9e28-74423c26a0d0', Sat Apr 13 2024 10:28:41 GMT+0800 (China Standard Time), Sat Apr 13 2024 10:28:50 GMT+0800 (China Standard Time), NULL, NULL, 0, 'RFID', 'RFID_9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '9e1dc8e6-0a0d-4149-90d5-0d7095cd07f1', 1, NULL, NULL, NULL, NULL, NULL),
--   ('986dd1f6-da71-40b3-a4c3-00b74dca134d', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:48 GMT+0800 (China Standard Time), NULL, NULL, 0, '创建项目', 'api.[POST]/system/v1/sysProject', 'API', NULL, '/system/v1/sysProject', 'POST', NULL, NULL, NULL, 'bc03d7f1-75a2-4646-95a7-63df5662fd5a', 1, NULL, NULL, NULL, NULL, NULL),
--   ('751bfc0a-e929-4596-8d3a-0f759f760df1', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:49 GMT+0800 (China Standard Time), NULL, NULL, 0, '批量删除公司', 'api.[POST]/wms/v1/wmsCompany/delete', 'API', NULL, '/wms/v1/wmsCompany/delete', 'POST', NULL, NULL, NULL, 'cfbe2c5f-f7e1-4041-8a81-b178d990abb0', 1, NULL, NULL, NULL, NULL, NULL),
--   ('3e705bd7-8335-49b6-ad76-5ee0f4ed41f7', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:50 GMT+0800 (China Standard Time), NULL, NULL, 0, '获取课程课件列表', 'api.[GET]/wms/v1/wmsLearningCourseCourseware', 'API', NULL, '/wms/v1/wmsLearningCourseCourseware', 'GET', NULL, NULL, NULL, '9de604c8-3ca6-4d19-adbb-7c60ff7a0ed8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('5e585ba1-c5b4-4962-9f14-fa3c7811016a', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:52 GMT+0800 (China Standard Time), NULL, NULL, 0, '更新装备类型属性', 'api.[PUT]/wms/v1/wmsEquipmentTypeProperty/{id}', 'API', NULL, '/wms/v1/wmsEquipmentTypeProperty/{id}', 'PUT', NULL, NULL, NULL, '3b3f1156-95b6-4258-b592-9c6049985dc3', 1, NULL, NULL, NULL, NULL, NULL),
--   ('cpak6hp97i61tji3q8pg', Tue May 28 2024 10:36:28 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:54 GMT+0800 (China Standard Time), NULL, NULL, 0, '获取我的仓库', 'api.[GET]/wms/v1/myWmsRepository', 'API', NULL, '/wms/v1/myWmsRepository', 'GET', NULL, NULL, NULL, '32d01350-f54c-4f4d-b5fb-5dc2632a692b', 1, NULL, NULL, NULL, NULL, NULL),
--   ('cpsk4dgv0kes8jgdtm00', Mon Jun 24 2024 17:53:36 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:54 GMT+0800 (China Standard Time), NULL, NULL, 0, '获取物料日志详情', 'api.[GET]/wms/v1/wmsMaterialLog/{id}', 'API', NULL, '/wms/v1/wmsMaterialLog/{id}', 'GET', NULL, NULL, NULL, 'bac807e0-d20a-4a21-a765-6201bce09fb3', 1, NULL, NULL, NULL, NULL, NULL),
--   ('ae0f47f1-0878-4b8d-a5bb-beb7f7bb3374', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:55 GMT+0800 (China Standard Time), NULL, NULL, 0, '批量删除省份', 'api.[POST]/system/v1/sysProvince/delete', 'API', NULL, '/system/v1/sysProvince/delete', 'POST', NULL, NULL, NULL, '486e6d7d-fe2a-489e-b0cb-fb208c766db4', 1, NULL, NULL, NULL, NULL, NULL),
--   ('1729c028-3dd8-4ab7-ae97-604916019c2f', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '公司地址', '公司地址_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('c7d06e56-e9ae-4bd0-8ded-2c25faea913d', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '字典详情', '字典详情_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('6f6f986a-b743-4d9f-85ec-e526f37f3e67', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '菜单管理', '菜单管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('87f46490-5aae-4637-904c-22d1f04f6f4d', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '装备管理', '装备管理_4b38fae2-eafd-4811-b5e3-639092c63786', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '4b38fae2-eafd-4811-b5e3-639092c63786', 1, NULL, NULL, NULL, NULL, NULL),
--   ('df058839-d45b-44aa-9560-31d87be278e2', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '设备人员组', '设备人员组_4b38fae2-eafd-4811-b5e3-639092c63786', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '4b38fae2-eafd-4811-b5e3-639092c63786', 1, NULL, NULL, NULL, NULL, NULL),
--   ('57b09b8e-9c4a-4cc0-aa86-76fcf12049e0', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '单位', '单位_5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '5bc04378-a6bd-4b1f-a612-bf54cd33c4e8', 1, NULL, NULL, NULL, NULL, NULL),
--   ('35831345-514a-4e90-aeee-5cea98e2c7b5', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '计量单位', '计量单位_57b09b8e-9c4a-4cc0-aa86-76fcf12049e0', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '57b09b8e-9c4a-4cc0-aa86-76fcf12049e0', 1, NULL, NULL, NULL, NULL, NULL),
--   ('420a8e62-adfc-4c59-9e2a-b9d21b24c810', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:09 GMT+0800 (China Standard Time), NULL, NULL, 0, '模块', '模块_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('4add4e4d-b2f0-4d5b-93af-a2dd4690add8', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:10 GMT+0800 (China Standard Time), NULL, NULL, 0, '装备详情', '装备详情_4b38fae2-eafd-4811-b5e3-639092c63786', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '4b38fae2-eafd-4811-b5e3-639092c63786', 1, NULL, NULL, NULL, NULL, NULL),
--   ('47204b39-3692-47b7-8246-8c85c833b579', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:10 GMT+0800 (China Standard Time), NULL, NULL, 0, '课程管理', '课程管理_48c61dfe-3108-4fd0-8170-d5231614b40e', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '48c61dfe-3108-4fd0-8170-d5231614b40e', 1, NULL, NULL, NULL, NULL, NULL),
--   ('ceae2810-8605-4cde-86bd-3f6b34615685', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:10 GMT+0800 (China Standard Time), NULL, NULL, 0, '标签管理', '标签管理_0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '0fd6673a-61c3-4d1a-84b7-4a4da2331af5', 1, NULL, NULL, NULL, NULL, NULL),
--   ('13403276-7a78-47b9-8e7e-b087f3a1fa38', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Mar 05 2024 16:49:11 GMT+0800 (China Standard Time), NULL, NULL, 0, '审批活动', '审批活动_2562c06c-503b-4833-862a-f47a23dfcf79', 'FOLDER', NULL, NULL, NULL, NULL, NULL, NULL, '2562c06c-503b-4833-862a-f47a23dfcf79', 1, NULL, NULL, NULL, NULL, NULL),
--   ('116b1107-5b71-46f7-ac6d-7a888ca5b158', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:47 GMT+0800 (China Standard Time), NULL, NULL, 0, '更新角色', 'api.[PUT]/system/v1/sysRole/{id}', 'API', NULL, '/system/v1/sysRole/{id}', 'PUT', NULL, NULL, NULL, 'ce5a051b-7f36-4981-9683-e3e694da83fa', 1, NULL, NULL, NULL, NULL, NULL),
--   ('663651a9-6962-4e5c-9132-b2931437c2e9', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:48 GMT+0800 (China Standard Time), NULL, NULL, 0, '获取用户信息', 'api.[POST]/system/v1/sysUser/getUserInfo', 'API', NULL, '/system/v1/sysUser/getUserInfo', 'POST', NULL, NULL, NULL, '0b2bb68c-3596-4b0f-9835-a1f2d115329c', 1, NULL, NULL, NULL, NULL, NULL),
--   ('10e7c253-8f22-4711-a47f-e4ca58658404', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:48 GMT+0800 (China Standard Time), NULL, 'zhangxin', 0, '更新装备类型', 'api.[PUT]/wms/v1/wmsEquipmentType/{id}', 'API', NULL, '/wms/v1/wmsEquipmentType/{id}', 'PUT', NULL, '', '', '74052fc0-4845-4f4b-99d7-4f2cd65a451d', 1, NULL, NULL, NULL, NULL, NULL),
--   ('9a8d4309-cd1d-4801-892f-adb693c74349', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:48 GMT+0800 (China Standard Time), NULL, NULL, 0, '获取团队详情', 'api.[GET]/system/v1/sysTeam/{id}', 'API', NULL, '/system/v1/sysTeam/{id}', 'GET', NULL, NULL, NULL, 'da27115e-8374-4b40-95c3-306e9b5dfb8a', 1, NULL, NULL, NULL, NULL, NULL),
--   ('805cd1d8-2f6f-4708-aa8b-305d6369f5aa', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:48 GMT+0800 (China Standard Time), NULL, NULL, 0, '更新团队', 'api.[PUT]/system/v1/sysTeam/{id}', 'API', NULL, '/system/v1/sysTeam/{id}', 'PUT', NULL, NULL, NULL, 'da27115e-8374-4b40-95c3-306e9b5dfb8a', 1, NULL, NULL, NULL, NULL, NULL),
--   ('903ff39e-1188-41fc-8129-d06a03e294f7', Tue Mar 05 2024 16:49:05 GMT+0800 (China Standard Time), Tue Jul 29 2025 17:22:48 GMT+0800 (China Standard Time), NULL, NULL, 0, '删除团队', 'api.[DELETE]/system/v1/sysTeam/{id}', 'API', NULL, '/system/v1/sysTeam/{id}', 'DELETE', NULL, NULL, NULL, 'da27115e-8374-4b40-95c3-306e9b5dfb8a', 1, NULL, NULL, NULL, NULL, NULL);

-- -- 向表 sys_role_menus 插入 5 条数据
-- -- 操作类型: INSERT_DATA
-- -- 表名: sys_role_menus
-- INSERT INTO "public"."sys_role_menus" ("id", "created_at", "updated_at", "created_by", "updated_by", "menu_id", "role_id") VALUES
--   ('b2ad1577-fb19-4ca1-a0ac-7dfe37d79822', Fri Dec 29 2023 12:24:19 GMT+0800 (China Standard Time), Fri Dec 29 2023 12:26:34 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', NULL, 'cb3eb916-0a52-44bf-815b-d14db57f2fec', 'a4a4331f-3256-4cce-a50b-3145dca7be43'),
--   ('f0e6391e-dcf1-48a0-aeca-df6aa5300592', Fri Dec 29 2023 12:24:19 GMT+0800 (China Standard Time), Fri Dec 29 2023 12:26:34 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', NULL, '9f0416ce-8812-4288-8131-da184e948687', 'a4a4331f-3256-4cce-a50b-3145dca7be43'),
--   ('80c88eab-7935-4d2b-9674-3f633e028a15', Fri Dec 29 2023 12:24:19 GMT+0800 (China Standard Time), Fri Dec 29 2023 12:26:34 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', NULL, 'c4c03d7a-bdcd-4147-b072-bf1e2f04fa8d', 'a4a4331f-3256-4cce-a50b-3145dca7be43'),
--   ('843e93ba-86c5-4055-b7fa-c50b0f3b9d5c', Fri Dec 29 2023 12:24:19 GMT+0800 (China Standard Time), Fri Dec 29 2023 12:26:34 GMT+0800 (China Standard Time), '2e6c5200-358f-11ec-981f-de2269351a1e', NULL, 'ce638074-35eb-4ba0-8476-775c614cd477', 'a4a4331f-3256-4cce-a50b-3145dca7be43'),
--   ('cs3jbprf7mcv52mfn2ug', Tue Oct 08 2024 16:18:21 GMT+0800 (China Standard Time), Thu Oct 10 2024 10:03:19 GMT+0800 (China Standard Time), NULL, 'cp5ckn29fp8s73e4ajqg', '3755dd8e-7d10-4d10-96c7-2b873493a42f', 'connj2q9fp8s73d6bs70');

