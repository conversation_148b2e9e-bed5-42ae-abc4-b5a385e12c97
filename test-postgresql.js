#!/usr/bin/env node

// PostgreSQL 连接测试脚本
import { PostgreSQLDriver } from './dist/database/driver.js';

console.log('🧪 测试 PostgreSQL 连接和查询...\n');

const driver = new PostgreSQLDriver();

// 测试用例
const testCases = [
  {
    name: 'PostgreSQL 连接字符串格式 1 (类似 MySQL)',
    config: {
      driver: 'postgresql',
      source: 'postgres:password@tcp(localhost:5432)/testdb'
    }
  },
  {
    name: 'PostgreSQL 连接字符串格式 2 (标准 URL)',
    config: {
      driver: 'postgresql',
      source: 'postgresql://user:pass@localhost:5432/database'
    }
  },
  {
    name: 'PostgreSQL 详细配置',
    config: {
      driver: 'postgresql',
      host: 'localhost',
      port: 5432,
      user: 'postgres',
      password: 'password',
      db: 'testdb'
    }
  }
];

for (const testCase of testCases) {
  console.log(`🔍 测试: ${testCase.name}`);
  
  try {
    if (testCase.config.source) {
      console.log(`   连接字符串: ${testCase.config.source}`);
      
      // 测试连接字符串解析
      const parsed = driver.parsePostgreSQLConnectionString(testCase.config.source);
      console.log('   ✅ 连接字符串解析成功！');
      console.log(`   📋 解析结果:`);
      console.log(`      主机: ${parsed.host}:${parsed.port}`);
      console.log(`      数据库: ${parsed.database}`);
      console.log(`      用户: ${parsed.user}`);
      console.log(`      密码: ${'*'.repeat(parsed.password?.length || 0)}`);
    } else {
      console.log(`   主机: ${testCase.config.host}:${testCase.config.port}`);
      console.log(`   数据库: ${testCase.config.db}`);
      console.log(`   用户: ${testCase.config.user}`);
    }
    
    // 注意：这里不进行实际连接测试，因为可能没有 PostgreSQL 服务器
    console.log('   ⚠️  跳过实际连接测试（需要 PostgreSQL 服务器）');
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
  }
  
  console.log('');
}

console.log('📝 要测试实际连接，请：');
console.log('1. 确保 PostgreSQL 服务器正在运行');
console.log('2. 在 config.yaml 中添加有效的 PostgreSQL 配置');
console.log('3. 运行: npm run dev');
console.log('');
console.log('PostgreSQL 配置示例:');
console.log('');
console.log('# 详细配置方式');
console.log('pg_local:');
console.log('  driver: postgresql');
console.log('  host: localhost');
console.log('  port: 5432');
console.log('  user: postgres');
console.log('  password: your_password');
console.log('  db: your_database');
console.log('');
console.log('# 连接字符串方式 (类似 MySQL 格式)');
console.log('pg_remote:');
console.log('  driver: postgresql');
console.log('  source: "postgres:password@tcp(localhost:5432)/database"');
