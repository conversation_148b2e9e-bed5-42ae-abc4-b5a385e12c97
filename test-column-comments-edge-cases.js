import { PostgreSQLGenerator } from './dist/migration/generator.js';

console.log('🧪 测试 PostgreSQL 列注释边界情况');
console.log('=====================================');

const generator = new PostgreSQLGenerator();

// 测试包含单引号的注释
console.log('📋 测试包含单引号的注释:');
const columnWithQuote = {
  name: 'description',
  type: 'TEXT',
  nullable: true,
  comment: "用户的'个人'描述信息"
};

const addColumnOp = generator.generateAddColumn('test_table', columnWithQuote);
console.log(addColumnOp.sql);
console.log('');

// 测试修改列注释
console.log('📋 测试修改列注释（从有注释改为有注释）:');
const oldColumnWithComment = {
  name: 'status',
  type: 'INT',
  nullable: false,
  comment: '状态'
};

const newColumnWithComment = {
  name: 'status',
  type: 'INT',
  nullable: false,
  comment: '用户状态：1-活跃，0-非活跃'
};

const modifyColumnOp1 = generator.generateModifyColumn('test_table', oldColumnWithComment, newColumnWithComment);
console.log(modifyColumnOp1.sql);
console.log('');

// 测试从无注释改为有注释
console.log('📋 测试修改列注释（从无注释改为有注释）:');
const oldColumnNoComment = {
  name: 'age',
  type: 'INT',
  nullable: true
};

const newColumnNewComment = {
  name: 'age',
  type: 'INT',
  nullable: true,
  comment: '年龄'
};

const modifyColumnOp2 = generator.generateModifyColumn('test_table', oldColumnNoComment, newColumnNewComment);
console.log(modifyColumnOp2.sql);
console.log('');

// 测试从有注释改为无注释
console.log('📋 测试修改列注释（从有注释改为无注释）:');
const oldColumnHasComment = {
  name: 'temp_field',
  type: 'VARCHAR',
  nullable: true,
  comment: '临时字段'
};

const newColumnNoComment = {
  name: 'temp_field',
  type: 'VARCHAR',
  nullable: true
  // 没有comment字段
};

const modifyColumnOp3 = generator.generateModifyColumn('test_table', oldColumnHasComment, newColumnNoComment);
console.log(modifyColumnOp3.sql);
console.log('');

console.log('✅ PostgreSQL 列注释边界情况测试完成！');