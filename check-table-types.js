#!/usr/bin/env node

// 检查实际的数据库表字段类型
import { createMetadata } from './dist/database/metadata.js';

console.log('🔍 检查 work_wx_approval_messages 表的实际字段类型...\n');

const config = {
  driver: 'postgresql',
  host: '***************',
  port: 5432,
  user: 'postgres',
  password: '123456',
  db: 'demo'
};

async function checkTableTypes() {
  try {
    console.log('🔌 连接数据库...');
    const metadata = await createMetadata(config);
    console.log('✅ 连接成功！\n');
    
    console.log('📊 获取 work_wx_approval_messages 表信息...');
    const tableInfo = await metadata.getTableInfo('work_wx_approval_messages');
    
    console.log('\n📋 字段类型详情:');
    tableInfo.columns.forEach(col => {
      console.log(`  ${col.name}: ${col.type} (nullable: ${col.nullable})`);
      if (col.defaultValue) {
        console.log(`    默认值: ${col.defaultValue}`);
      }
    });
    
    console.log('\n🔍 重点检查的字段:');
    const bigintFields = ['create_time', 'agent_id', 'open_sp_status', 'apply_time', 'approver_step'];
    bigintFields.forEach(fieldName => {
      const field = tableInfo.columns.find(col => col.name === fieldName);
      if (field) {
        console.log(`  ${fieldName}: ${field.type}`);
      } else {
        console.log(`  ${fieldName}: 字段不存在`);
      }
    });
    
  } catch (error) {
    console.log('❌ 检查失败:', error.message);
    console.log('错误详情:', error);
  }
}

checkTableTypes();
