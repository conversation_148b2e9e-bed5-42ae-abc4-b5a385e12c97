-- 测试修复后的 PostgreSQL SQL 语法
-- 这些语句应该在 PostgreSQL 中正确执行

-- 创建测试表
CREATE TABLE "test_table" (
  "id" INTEGER NOT NULL,
  "name" CHARACTER VARYING(255) NOT NULL,
  "age" INTEGER,
  "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
  "data" JSONB,
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  PRIMARY KEY ("id")
);

-- 添加字段
ALTER TABLE "test_table" ADD COLUMN "email" CHARACTER VARYING(255);

-- 修改字段类型
ALTER TABLE "test_table" ALTER COLUMN "age" TYPE BIGINT;

-- 修改字段约束
ALTER TABLE "test_table" ALTER COLUMN "age" SET NOT NULL;

-- 设置默认值
ALTER TABLE "test_table" ALTER COLUMN "age" SET DEFAULT 0;

-- 创建索引
CREATE INDEX "idx_name" ON "test_table" ("name");
CREATE UNIQUE INDEX "idx_email" ON "test_table" ("email");

-- 插入测试数据
INSERT INTO "test_table" ("id", "name", "age", "is_active", "email") VALUES
  (1, 'John Doe', 30, TRUE, '<EMAIL>'),
  (2, 'Jane Smith', 25, FALSE, '<EMAIL>');

-- 清理测试数据
DROP TABLE IF EXISTS "test_table";
