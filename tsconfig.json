{"compilerOptions": {"target": "ES2022", "module": "Node16", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "moduleResolution": "node16", "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "resolveJsonModule": true, "noImplicitAny": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}