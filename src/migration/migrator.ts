import { DatabaseConfig } from '../types/config.js';
import { MigrationConfig, MigrationOperation, DatabaseMetadata } from '../types/migration.js';
import { createMetadata } from '../database/metadata.js';
import { DatabaseComparator } from './comparator.js';
import { createSQLGenerator } from './generator.js';
import { MigrationManager } from './manager.js';

/**
 * 数据库迁移器
 */
export class DatabaseMigrator {
  private sourceConfig: DatabaseConfig;
  private targetConfig: DatabaseConfig;
  private migrationConfig: MigrationConfig;

  constructor(
    sourceConfig: DatabaseConfig,
    targetConfig: DatabaseConfig,
    migrationConfig: MigrationConfig
  ) {
    this.sourceConfig = sourceConfig;
    this.targetConfig = targetConfig;
    this.migrationConfig = migrationConfig;
  }

  /**
   * 执行数据库迁移分析
   */
  async analyze(): Promise<{
    operations: MigrationOperation[];
    summary: string;
    filePath?: string;
  }> {
    console.log('🔍 开始分析数据库差异...');

    let sourceMetadata: DatabaseMetadata | null = null;
    let targetMetadata: DatabaseMetadata | null = null;

    try {
      // 合并表过滤配置
      const sourceConfigWithFilter = {
        ...this.sourceConfig,
        excludeTables: this.migrationConfig.excludeTables || this.sourceConfig.excludeTables,
        includeTables: this.migrationConfig.includeTables || this.sourceConfig.includeTables
      };

      const targetConfigWithFilter = {
        ...this.targetConfig,
        excludeTables: this.migrationConfig.excludeTables || this.targetConfig.excludeTables,
        includeTables: this.migrationConfig.includeTables || this.targetConfig.includeTables
      };

      // 创建数据库元数据查询器
      const sourceDbInfo = sourceConfigWithFilter.source || `${sourceConfigWithFilter.host}:${sourceConfigWithFilter.port}/${sourceConfigWithFilter.db}`;
      console.log(`📊 连接源数据库: ${sourceConfigWithFilter.driver}://${sourceDbInfo}`);
      if (sourceConfigWithFilter.excludeTables && sourceConfigWithFilter.excludeTables.length > 0) {
        console.log(`🚫 排除表: ${sourceConfigWithFilter.excludeTables.join(', ')}`);
      }
      if (sourceConfigWithFilter.includeTables && sourceConfigWithFilter.includeTables.length > 0) {
        console.log(`✅ 仅包含表: ${sourceConfigWithFilter.includeTables.join(', ')}`);
      }
      sourceMetadata = await createMetadata(sourceConfigWithFilter);

      const targetDbInfo = targetConfigWithFilter.source || `${targetConfigWithFilter.host}:${targetConfigWithFilter.port}/${targetConfigWithFilter.db}`;
      console.log(`📊 连接目标数据库: ${targetConfigWithFilter.driver}://${targetDbInfo}`);
      if (targetConfigWithFilter.excludeTables && targetConfigWithFilter.excludeTables.length > 0) {
        console.log(`🚫 排除表: ${targetConfigWithFilter.excludeTables.join(', ')}`);
      }
      if (targetConfigWithFilter.includeTables && targetConfigWithFilter.includeTables.length > 0) {
        console.log(`✅ 仅包含表: ${targetConfigWithFilter.includeTables.join(', ')}`);
      }
      targetMetadata = await createMetadata(targetConfigWithFilter);

      // 比较数据库
      console.log('🔄 比较数据库结构...');
      const comparator = new DatabaseComparator(sourceMetadata, targetMetadata);
      const diff = await comparator.compare(this.migrationConfig.includeData);

      // 生成 SQL
      console.log('📝 生成迁移 SQL...');
      const generator = createSQLGenerator(this.targetConfig.driver);
      const operations = generator.generateMigrationOperations(diff);

      // 生成摘要
      const manager = new MigrationManager(this.migrationConfig);
      const summary = manager.generateSummary(operations);

      console.log('✅ 分析完成！');
      console.log('\n' + summary);

      // 如果有操作，生成迁移文件
      let filePath: string | undefined;
      if (operations.length > 0) {
        console.log('\n📁 生成迁移文件...');
        filePath = await manager.generateMigrationFile(operations);
        console.log(`✅ 迁移文件已生成: ${filePath}`);
      } else {
        console.log('\n✨ 数据库结构一致，无需迁移！');
      }

      return {
        operations,
        summary,
        filePath
      };
    } finally {
      // 确保关闭数据库连接
      if (sourceMetadata) {
        try {
          await sourceMetadata.close();
        } catch (error) {
          console.warn('关闭源数据库连接时出错:', error instanceof Error ? error.message : error);
        }
      }
      if (targetMetadata) {
        try {
          await targetMetadata.close();
        } catch (error) {
          console.warn('关闭目标数据库连接时出错:', error instanceof Error ? error.message : error);
        }
      }
    }
  }

  /**
   * 验证配置
   */
  static validateConfig(
    sourceConfig: DatabaseConfig,
    targetConfig: DatabaseConfig,
    migrationConfig: MigrationConfig
  ): void {
    // 验证源数据库配置
    if (!sourceConfig.driver) {
      throw new Error('源数据库配置缺少 driver 字段');
    }

    // 如果没有source，则检查其他必需字段
    if (!sourceConfig.source) {
      if (!sourceConfig.host || !sourceConfig.db) {
        throw new Error('源数据库配置不完整，需要 host、db 字段，或者使用 source 连接字符串');
      }
    }

    // 验证目标数据库配置
    if (!targetConfig.driver) {
      throw new Error('目标数据库配置缺少 driver 字段');
    }

    // 如果没有source，则检查其他必需字段
    if (!targetConfig.source) {
      if (!targetConfig.host || !targetConfig.db) {
        throw new Error('目标数据库配置不完整，需要 host、db 字段，或者使用 source 连接字符串');
      }
    }

    // 验证迁移配置
    if (!migrationConfig.outputDir) {
      throw new Error('迁移配置缺少输出目录 outputDir');
    }

    // 检查数据库类型兼容性
    const supportedDrivers = ['mysql', 'postgresql', 'postgres'];
    if (!supportedDrivers.includes(sourceConfig.driver)) {
      throw new Error(`不支持的源数据库驱动: ${sourceConfig.driver}`);
    }
    if (!supportedDrivers.includes(targetConfig.driver)) {
      throw new Error(`不支持的目标数据库驱动: ${targetConfig.driver}`);
    }

    // 警告：跨数据库类型迁移
    if (sourceConfig.driver !== targetConfig.driver) {
      console.log('⚠️  警告: 检测到跨数据库类型迁移，请仔细检查生成的 SQL 语句');
    }
  }

  /**
   * 从配置中提取数据库名
   */
  static extractDatabaseName(config: DatabaseConfig): string {
    if (config.db) {
      return config.db;
    }

    if (config.source) {
      // 从连接字符串中提取数据库名
      if (config.source.includes('@tcp(')) {
        // 类似 MySQL 格式: user:password@tcp(host:port)/database
        const match = config.source.match(/@tcp\([^)]+\)\/([^?]+)/);
        if (match) {
          return match[1];
        }
      } else if (config.source.includes('://')) {
        // 标准 URL 格式: protocol://user:password@host:port/database
        try {
          const url = new URL(config.source);
          return url.pathname.slice(1); // 移除开头的 '/'
        } catch {
          // 忽略 URL 解析错误
        }
      }
    }

    throw new Error('无法从配置中提取数据库名');
  }

  /**
   * 创建迁移器实例
   */
  static create(
    sourceConfig: DatabaseConfig,
    targetConfig: DatabaseConfig,
    options: {
      outputDir: string;
      includeData?: boolean;
      compareData?: boolean;
      excludeTables?: string[];
      includeTables?: string[];
    }
  ): DatabaseMigrator {
    const migrationConfig: MigrationConfig = {
      sourceDb: this.extractDatabaseName(sourceConfig),
      targetDb: this.extractDatabaseName(targetConfig),
      outputDir: options.outputDir,
      includeData: options.includeData || false,
      compareData: options.compareData || false,
      excludeTables: options.excludeTables,
      includeTables: options.includeTables
    };

    // 验证配置
    this.validateConfig(sourceConfig, targetConfig, migrationConfig);

    return new DatabaseMigrator(sourceConfig, targetConfig, migrationConfig);
  }
}

/**
 * 快速迁移函数
 */
export async function migrate(
  sourceConfig: DatabaseConfig,
  targetConfig: DatabaseConfig,
  outputDir: string,
  options: {
    includeData?: boolean;
    compareData?: boolean;
    excludeTables?: string[];
    includeTables?: string[];
  } = {}
): Promise<{
  operations: MigrationOperation[];
  summary: string;
  filePath?: string;
}> {
  const migrator = DatabaseMigrator.create(sourceConfig, targetConfig, {
    outputDir,
    ...options
  });

  return await migrator.analyze();
}
