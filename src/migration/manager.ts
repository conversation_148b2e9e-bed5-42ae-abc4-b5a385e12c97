import fs from 'fs/promises';
import path from 'path';
import { MigrationFile, MigrationOperation, MigrationConfig } from '../types/migration.js';

/**
 * 迁移文件管理器
 */
export class MigrationManager {
  private config: MigrationConfig;

  constructor(config: MigrationConfig) {
    this.config = config;
  }

  /**
   * 生成迁移文件
   */
  async generateMigrationFile(operations: MigrationOperation[]): Promise<string> {
    // 确保输出目录存在
    await this.ensureOutputDirectory();

    // 生成时间戳
    const timestamp = this.generateTimestamp();
    
    // 生成文件名
    const filename = `${timestamp}_migrate_${this.config.sourceDb}_to_${this.config.targetDb}.sql`;
    const filepath = path.join(this.config.outputDir, filename);

    // 生成文件内容
    const content = this.generateFileContent(operations, timestamp);

    // 写入文件
    await fs.writeFile(filepath, content, 'utf8');

    return filepath;
  }

  /**
   * 生成时间戳
   */
  private generateTimestamp(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    
    return `${year}${month}${day}_${hour}${minute}${second}`;
  }

  /**
   * 确保输出目录存在
   */
  private async ensureOutputDirectory(): Promise<void> {
    try {
      await fs.access(this.config.outputDir);
    } catch {
      await fs.mkdir(this.config.outputDir, { recursive: true });
    }
  }

  /**
   * 生成文件内容
   */
  private generateFileContent(operations: MigrationOperation[], timestamp: string): string {
    const header = this.generateHeader(timestamp);
    const sqlStatements = operations
      .filter(op => op.sql.trim().length > 0)
      .map(op => this.formatOperation(op))
      .join('\n\n');

    return `${header}\n\n${sqlStatements}\n`;
  }

  /**
   * 生成文件头部注释
   */
  private generateHeader(timestamp: string): string {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 19).replace('T', ' ');

    return `-- =====================================================
-- 数据库迁移文件
-- =====================================================
-- 生成时间: ${dateStr}
-- 时间戳: ${timestamp}
-- 源数据库: ${this.config.sourceDb}
-- 目标数据库: ${this.config.targetDb}
-- 生成工具: dbtool migrate
-- =====================================================
-- 
-- 注意事项:
-- 1. 请在执行前备份目标数据库
-- 2. 建议在测试环境中先验证迁移脚本
-- 3. 执行前请检查所有 SQL 语句的正确性
-- 4. 如有数据迁移，请注意数据一致性
-- 
-- =====================================================`;
  }

  /**
   * 格式化单个操作
   */
  private formatOperation(operation: MigrationOperation): string {
    const comment = `-- ${operation.description}`;
    const typeComment = `-- 操作类型: ${operation.type}`;
    
    let tableInfo = '';
    if (operation.tableName) {
      tableInfo = `-- 表名: ${operation.tableName}`;
    }
    
    let columnInfo = '';
    if (operation.columnName) {
      columnInfo = `-- 字段名: ${operation.columnName}`;
    }
    
    let indexInfo = '';
    if (operation.indexName) {
      indexInfo = `-- 索引名: ${operation.indexName}`;
    }

    const comments = [comment, typeComment, tableInfo, columnInfo, indexInfo]
      .filter(c => c.length > 3)
      .join('\n');

    return `${comments}\n${operation.sql}`;
  }

  /**
   * 列出现有的迁移文件
   */
  async listMigrationFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.config.outputDir);
      return files
        .filter(file => file.endsWith('.sql') && file.includes('migrate'))
        .sort()
        .reverse(); // 最新的在前面
    } catch {
      return [];
    }
  }

  /**
   * 读取迁移文件内容
   */
  async readMigrationFile(filename: string): Promise<string> {
    const filepath = path.join(this.config.outputDir, filename);
    return await fs.readFile(filepath, 'utf8');
  }

  /**
   * 生成迁移摘要
   */
  generateSummary(operations: MigrationOperation[]): string {
    const summary = {
      createTables: 0,
      alterTables: 0,
      addColumns: 0,
      modifyColumns: 0,
      dropColumns: 0,
      addIndexes: 0,
      dropIndexes: 0,
      insertData: 0,
      updateData: 0
    };

    operations.forEach(op => {
      switch (op.type) {
        case 'CREATE_TABLE':
          summary.createTables++;
          break;
        case 'ALTER_TABLE':
          summary.alterTables++;
          break;
        case 'ADD_COLUMN':
          summary.addColumns++;
          break;
        case 'MODIFY_COLUMN':
          summary.modifyColumns++;
          break;
        case 'DROP_COLUMN':
          summary.dropColumns++;
          break;
        case 'ADD_INDEX':
          summary.addIndexes++;
          break;
        case 'DROP_INDEX':
          summary.dropIndexes++;
          break;
        case 'INSERT_DATA':
          summary.insertData++;
          break;
        case 'UPDATE_DATA':
          summary.updateData++;
          break;
      }
    });

    const summaryLines = [
      `迁移操作摘要:`,
      `- 创建表: ${summary.createTables}`,
      `- 修改表: ${summary.alterTables}`,
      `- 添加字段: ${summary.addColumns}`,
      `- 修改字段: ${summary.modifyColumns}`,
      `- 删除字段: ${summary.dropColumns}`,
      `- 添加索引: ${summary.addIndexes}`,
      `- 删除索引: ${summary.dropIndexes}`,
      `- 插入数据: ${summary.insertData}`,
      `- 更新数据: ${summary.updateData}`,
      `- 总操作数: ${operations.length}`
    ];

    return summaryLines.join('\n');
  }
}
