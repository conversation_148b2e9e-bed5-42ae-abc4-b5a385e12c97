import { 
  DatabaseMetadata, 
  TableInfo, 
  ColumnInfo, 
  MigrationDiff, 
  TableModification, 
  ColumnModification,
  DataChange 
} from '../types/migration.js';

/**
 * 数据库比较器
 */
export class DatabaseComparator {
  private sourceMetadata: DatabaseMetadata;
  private targetMetadata: DatabaseMetadata;

  constructor(sourceMetadata: DatabaseMetadata, targetMetadata: DatabaseMetadata) {
    this.sourceMetadata = sourceMetadata;
    this.targetMetadata = targetMetadata;
  }

  /**
   * 比较两个数据库的差异
   */
  async compare(includeData: boolean = false): Promise<MigrationDiff> {
    // 获取源数据库和目标数据库的表列表
    const sourceTables = await this.sourceMetadata.getTables();
    const targetTables = await this.targetMetadata.getTables();

    // 找出新表、修改的表和删除的表
    const newTables: TableInfo[] = [];
    const modifiedTables: TableModification[] = [];
    const droppedTables: string[] = [];

    // 找出新表
    for (const tableName of sourceTables) {
      if (!targetTables.includes(tableName)) {
        const tableInfo = await this.sourceMetadata.getTableInfo(tableName);
        newTables.push(tableInfo);
      }
    }

    // 找出删除的表
    for (const tableName of targetTables) {
      if (!sourceTables.includes(tableName)) {
        droppedTables.push(tableName);
      }
    }

    // 比较共同的表
    const commonTables = sourceTables.filter(table => targetTables.includes(table));
    for (const tableName of commonTables) {
      const sourceTable = await this.sourceMetadata.getTableInfo(tableName);
      const targetTable = await this.targetMetadata.getTableInfo(tableName);
      
      const modification = this.compareTable(sourceTable, targetTable);
      if (this.hasTableModifications(modification)) {
        modifiedTables.push(modification);
      }
    }

    // 比较数据（如果需要）
    const dataChanges: DataChange[] = [];
    if (includeData) {
      for (const tableName of commonTables) {
        const changes = await this.compareTableData(tableName);
        if (changes.length > 0) {
          dataChanges.push(...changes);
        }
      }
    }

    return {
      newTables,
      modifiedTables,
      droppedTables,
      dataChanges
    };
  }

  /**
   * 比较单个表的结构
   */
  private compareTable(sourceTable: TableInfo, targetTable: TableInfo): TableModification {
    const sourceColumns = sourceTable.columns;
    const targetColumns = targetTable.columns;
    const sourceIndexes = sourceTable.indexes;
    const targetIndexes = targetTable.indexes;

    // 比较字段
    const newColumns: ColumnInfo[] = [];
    const modifiedColumns: ColumnModification[] = [];
    const droppedColumns: string[] = [];

    // 找出新字段
    for (const sourceColumn of sourceColumns) {
      const targetColumn = targetColumns.find(col => col.name === sourceColumn.name);
      if (!targetColumn) {
        newColumns.push(sourceColumn);
      } else {
        // 比较字段是否有变化
        if (!this.areColumnsEqual(sourceColumn, targetColumn)) {
          modifiedColumns.push({
            columnName: sourceColumn.name,
            oldColumn: targetColumn,
            newColumn: sourceColumn
          });
        }
      }
    }

    // 找出删除的字段
    for (const targetColumn of targetColumns) {
      if (!sourceColumns.find(col => col.name === targetColumn.name)) {
        droppedColumns.push(targetColumn.name);
      }
    }

    // 比较索引
    const newIndexes = sourceIndexes.filter(sourceIndex => 
      !targetIndexes.find(targetIndex => this.areIndexesEqual(sourceIndex, targetIndex))
    );

    const droppedIndexes = targetIndexes
      .filter(targetIndex => 
        !sourceIndexes.find(sourceIndex => this.areIndexesEqual(sourceIndex, targetIndex))
      )
      .map(index => index.name);

    return {
      tableName: sourceTable.name,
      newColumns,
      modifiedColumns,
      droppedColumns,
      newIndexes,
      droppedIndexes
    };
  }

  /**
   * 比较两个字段是否相等
   */
  private areColumnsEqual(col1: ColumnInfo, col2: ColumnInfo): boolean {
    return (
      col1.name === col2.name &&
      col1.type === col2.type &&
      col1.nullable === col2.nullable &&
      col1.defaultValue === col2.defaultValue &&
      col1.autoIncrement === col2.autoIncrement
    );
  }

  /**
   * 比较两个索引是否相等
   */
  private areIndexesEqual(idx1: any, idx2: any): boolean {
    return (
      idx1.name === idx2.name &&
      idx1.unique === idx2.unique &&
      JSON.stringify(idx1.columns.sort()) === JSON.stringify(idx2.columns.sort())
    );
  }

  /**
   * 检查表是否有修改
   */
  private hasTableModifications(modification: TableModification): boolean {
    return (
      modification.newColumns.length > 0 ||
      modification.modifiedColumns.length > 0 ||
      modification.droppedColumns.length > 0 ||
      modification.newIndexes.length > 0 ||
      modification.droppedIndexes.length > 0
    );
  }

  /**
   * 比较表数据（简化版本，实际实现会更复杂）
   */
  private async compareTableData(tableName: string): Promise<DataChange[]> {
    // 这里是一个简化的实现
    // 实际应用中需要更复杂的数据比较逻辑
    const sourceCount = await this.sourceMetadata.getRowCount(tableName);
    const targetCount = await this.targetMetadata.getRowCount(tableName);

    const changes: DataChange[] = [];

    // 如果源数据库有更多数据，可能需要插入
    if (sourceCount > targetCount) {
      const sourceData = await this.sourceMetadata.getTableData(tableName, sourceCount - targetCount);
      if (sourceData.length > 0) {
        changes.push({
          tableName,
          type: 'INSERT',
          data: sourceData
        });
      }
    }

    return changes;
  }
}
