import {
  MigrationDiff,
  MigrationOperation,
  MigrationType,
  TableInfo,
  ColumnInfo,
  IndexInfo,
  TableModification,
  DataChange,
  PrimaryKeyChange
} from '../types/migration.js';

/**
 * SQL 生成器基类
 */
export abstract class SQLGenerator {
  abstract generateCreateTable(table: TableInfo): MigrationOperation;
  abstract generateAddColumn(tableName: string, column: ColumnInfo): MigrationOperation;
  abstract generateModifyColumn(tableName: string, oldColumn: ColumnInfo, newColumn: ColumnInfo): MigrationOperation;
  abstract generateDropColumn(tableName: string, columnName: string): MigrationOperation;
  abstract generateAddIndex(tableName: string, index: IndexInfo): MigrationOperation;
  abstract generateDropIndex(tableName: string, indexName: string): MigrationOperation;
  abstract generateAddPrimaryKey(tableName: string, columns: string[]): MigrationOperation;
  abstract generateDropPrimaryKey(tableName: string): MigrationOperation;
  abstract generateInsertData(tableName: string, data: Record<string, any>[]): MigrationOperation;
  
  /**
   * 生成完整的迁移操作列表
   */
  generateMigrationOperations(diff: MigrationDiff): MigrationOperation[] {
    const operations: MigrationOperation[] = [];

    // 1. 创建新表
    for (const table of diff.newTables) {
      operations.push(this.generateCreateTable(table));
    }

    // 2. 修改现有表 - 分阶段处理以确保正确的操作顺序
    this.generateTableModificationsInOrder(diff.modifiedTables, operations);

    // 3. 插入数据
    for (const dataChange of diff.dataChanges) {
      if (dataChange.type === 'INSERT') {
        operations.push(this.generateInsertData(dataChange.tableName, dataChange.data));
      }
    }

    return operations;
  }

  /**
   * 按表排序生成修改操作，每个表内部删除操作在前，添加操作在后
   */
  private generateTableModificationsInOrder(modifications: TableModification[], operations: MigrationOperation[]): void {
    // 按表名排序，确保操作顺序的一致性
    const sortedModifications = modifications.sort((a, b) => a.tableName.localeCompare(b.tableName));

    // 对每个表，按照删除->修改->添加的顺序处理
    for (const modification of sortedModifications) {
      // 1. 删除操作（索引、主键、字段）

      // 删除索引
      for (const indexName of modification.droppedIndexes) {
        operations.push(this.generateDropIndex(modification.tableName, indexName));
      }

      // 删除主键
      if (modification.primaryKeyChange) {
        const pkChange = modification.primaryKeyChange;
        if (pkChange.type === 'MODIFY' || pkChange.type === 'DROP') {
          operations.push(this.generateDropPrimaryKey(modification.tableName));
        }
      }

      // 删除字段
      for (const columnName of modification.droppedColumns) {
        operations.push(this.generateDropColumn(modification.tableName, columnName));
      }

      // 2. 修改操作

      // 修改字段
      for (const columnMod of modification.modifiedColumns) {
        operations.push(this.generateModifyColumn(
          modification.tableName,
          columnMod.oldColumn,
          columnMod.newColumn
        ));
      }

      // 3. 添加操作（字段、主键、索引）

      // 添加字段
      for (const column of modification.newColumns) {
        operations.push(this.generateAddColumn(modification.tableName, column));
      }

      // 添加主键
      if (modification.primaryKeyChange) {
        const pkChange = modification.primaryKeyChange;
        if ((pkChange.type === 'ADD' || pkChange.type === 'MODIFY') && pkChange.newPrimaryKey) {
          operations.push(this.generateAddPrimaryKey(modification.tableName, pkChange.newPrimaryKey));
        }
      }

      // 添加索引
      for (const index of modification.newIndexes) {
        operations.push(this.generateAddIndex(modification.tableName, index));
      }
    }
  }


}

/**
 * MySQL SQL 生成器
 */
export class MySQLGenerator extends SQLGenerator {
  generateCreateTable(table: TableInfo): MigrationOperation {
    let sql = `CREATE TABLE \`${table.name}\` (\n`;
    
    // 字段定义
    const columnDefs = table.columns.map(col => this.formatColumnDefinition(col));
    sql += columnDefs.map(def => `  ${def}`).join(',\n');

    // 主键
    if (table.primaryKey && table.primaryKey.length > 0) {
      const pkColumns = table.primaryKey.map(col => `\`${col}\``).join(', ');
      sql += `,\n  PRIMARY KEY (${pkColumns})`;
    }

    // 索引
    for (const index of table.indexes) {
      const indexType = index.unique ? 'UNIQUE KEY' : 'KEY';
      const indexColumns = index.columns.map(col => `\`${col}\``).join(', ');
      sql += `,\n  ${indexType} \`${index.name}\` (${indexColumns})`;
    }

    sql += '\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;';

    return {
      type: MigrationType.CREATE_TABLE,
      tableName: table.name,
      sql,
      description: `创建表 ${table.name}`
    };
  }

  generateAddColumn(tableName: string, column: ColumnInfo): MigrationOperation {
    const columnDef = this.formatColumnDefinition(column);
    const sql = `ALTER TABLE \`${tableName}\` ADD COLUMN ${columnDef};`;

    return {
      type: MigrationType.ADD_COLUMN,
      tableName,
      columnName: column.name,
      sql,
      description: `在表 ${tableName} 中添加字段 ${column.name}`
    };
  }

  generateModifyColumn(tableName: string, oldColumn: ColumnInfo, newColumn: ColumnInfo): MigrationOperation {
    const columnDef = this.formatColumnDefinition(newColumn);
    const sql = `ALTER TABLE \`${tableName}\` MODIFY COLUMN ${columnDef};`;

    return {
      type: MigrationType.MODIFY_COLUMN,
      tableName,
      columnName: newColumn.name,
      sql,
      description: `修改表 ${tableName} 中的字段 ${newColumn.name}`
    };
  }

  generateDropColumn(tableName: string, columnName: string): MigrationOperation {
    const sql = `ALTER TABLE \`${tableName}\` DROP COLUMN \`${columnName}\`;`;

    return {
      type: MigrationType.DROP_COLUMN,
      tableName,
      columnName,
      sql,
      description: `删除表 ${tableName} 中的字段 ${columnName}`
    };
  }

  generateAddIndex(tableName: string, index: IndexInfo): MigrationOperation {
    const indexType = index.unique ? 'UNIQUE INDEX' : 'INDEX';
    const indexColumns = index.columns.map(col => `\`${col}\``).join(', ');
    const sql = `CREATE ${indexType} \`${index.name}\` ON \`${tableName}\` (${indexColumns});`;

    return {
      type: MigrationType.ADD_INDEX,
      tableName,
      indexName: index.name,
      sql,
      description: `在表 ${tableName} 上创建索引 ${index.name}`
    };
  }

  generateDropIndex(tableName: string, indexName: string): MigrationOperation {
    const sql = `DROP INDEX \`${indexName}\` ON \`${tableName}\`;`;

    return {
      type: MigrationType.DROP_INDEX,
      tableName,
      indexName,
      sql,
      description: `删除表 ${tableName} 上的索引 ${indexName}`
    };
  }

  generateAddPrimaryKey(tableName: string, columns: string[]): MigrationOperation {
    const pkColumns = columns.map(col => `\`${col}\``).join(', ');
    const sql = `ALTER TABLE \`${tableName}\` ADD PRIMARY KEY (${pkColumns});`;

    return {
      type: MigrationType.ADD_PRIMARY_KEY,
      tableName,
      sql,
      description: `在表 ${tableName} 上添加主键 (${columns.join(', ')})`
    };
  }

  generateDropPrimaryKey(tableName: string): MigrationOperation {
    const sql = `ALTER TABLE \`${tableName}\` DROP PRIMARY KEY;`;

    return {
      type: MigrationType.DROP_PRIMARY_KEY,
      tableName,
      sql,
      description: `删除表 ${tableName} 的主键`
    };
  }

  generateInsertData(tableName: string, data: Record<string, any>[]): MigrationOperation {
    if (data.length === 0) {
      return {
        type: MigrationType.INSERT_DATA,
        tableName,
        sql: '',
        description: `表 ${tableName} 无数据需要插入`
      };
    }

    const columns = Object.keys(data[0]);
    const columnList = columns.map(col => `\`${col}\``).join(', ');
    
    const values = data.map(row => {
      const rowValues = columns.map(col => this.formatValue(row[col])).join(', ');
      return `(${rowValues})`;
    }).join(',\n  ');

    const sql = `INSERT INTO \`${tableName}\` (${columnList}) VALUES\n  ${values};`;

    return {
      type: MigrationType.INSERT_DATA,
      tableName,
      sql,
      description: `向表 ${tableName} 插入 ${data.length} 条数据`
    };
  }

  private formatColumnDefinition(column: ColumnInfo): string {
    let def = `\`${column.name}\` ${column.type.toUpperCase()}`;

    if (!column.nullable) {
      def += ' NOT NULL';
    }

    if (column.defaultValue !== undefined && column.defaultValue !== null) {
      def += ` DEFAULT ${this.formatValue(column.defaultValue)}`;
    }

    if (column.autoIncrement) {
      def += ' AUTO_INCREMENT';
    }

    if (column.comment) {
      def += ` COMMENT '${column.comment.replace(/'/g, "''")}'`;
    }

    return def;
  }

  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'NULL';
    }
    if (typeof value === 'string') {
      return `'${value.replace(/'/g, "''")}'`;
    }
    if (typeof value === 'boolean') {
      return value ? '1' : '0';
    }
    return String(value);
  }
}

/**
 * PostgreSQL SQL 生成器
 */
export class PostgreSQLGenerator extends SQLGenerator {
  generateCreateTable(table: TableInfo): MigrationOperation {
    let sql = `CREATE TABLE "public"."${table.name}" (\n`;

    // 字段定义
    const columnDefs = table.columns.map(col => this.formatColumnDefinition(col));
    sql += columnDefs.map(def => `  ${def}`).join(',\n');

    // 主键
    if (table.primaryKey && table.primaryKey.length > 0) {
      const pkColumns = table.primaryKey.map(col => `"${col}"`).join(', ');
      sql += `,\n  PRIMARY KEY (${pkColumns})`;
    }

    sql += '\n);';

    // 添加列注释
    const commentStatements = this.generateColumnComments(table.name, table.columns);
    if (commentStatements.length > 0) {
      sql += '\n\n' + commentStatements.join('\n');
    }

    return {
      type: MigrationType.CREATE_TABLE,
      tableName: table.name,
      sql,
      description: `创建表 ${table.name}`
    };
  }

  generateAddColumn(tableName: string, column: ColumnInfo): MigrationOperation {
    const columnDef = this.formatColumnDefinition(column);
    let sql = `ALTER TABLE "public"."${tableName}" ADD COLUMN ${columnDef};`;

    // 添加列注释
    if (column.comment) {
      sql += `\nCOMMENT ON COLUMN "public"."${tableName}"."${column.name}" IS '${column.comment.replace(/'/g, "''")}';`;
    }

    return {
      type: MigrationType.ADD_COLUMN,
      tableName,
      columnName: column.name,
      sql,
      description: `在表 ${tableName} 中添加字段 ${column.name}`
    };
  }

  generateModifyColumn(tableName: string, oldColumn: ColumnInfo, newColumn: ColumnInfo): MigrationOperation {
    // PostgreSQL 需要分步修改字段
    let sql = '';

    // 修改类型
    if (oldColumn.type !== newColumn.type) {
      const pgType = this.convertToPostgreSQLType(newColumn.type);
      sql += `ALTER TABLE "public"."${tableName}" ALTER COLUMN "${newColumn.name}" TYPE ${pgType};\n`;
    }

    // 修改 NULL 约束
    if (oldColumn.nullable !== newColumn.nullable) {
      if (newColumn.nullable) {
        sql += `ALTER TABLE "public"."${tableName}" ALTER COLUMN "${newColumn.name}" DROP NOT NULL;\n`;
      } else {
        sql += `ALTER TABLE "public"."${tableName}" ALTER COLUMN "${newColumn.name}" SET NOT NULL;\n`;
      }
    }

    // 修改默认值
    if (oldColumn.defaultValue !== newColumn.defaultValue) {
      if (newColumn.defaultValue !== undefined && newColumn.defaultValue !== null) {
        sql += `ALTER TABLE "public"."${tableName}" ALTER COLUMN "${newColumn.name}" SET DEFAULT ${this.formatValue(newColumn.defaultValue)};\n`;
      } else {
        sql += `ALTER TABLE "public"."${tableName}" ALTER COLUMN "${newColumn.name}" DROP DEFAULT;\n`;
      }
    }

    // 修改注释
    if (oldColumn.comment !== newColumn.comment) {
      if (newColumn.comment) {
        const escapedComment = newColumn.comment.replace(/'/g, "''");
        sql += `COMMENT ON COLUMN "public"."${tableName}"."${newColumn.name}" IS '${escapedComment}';\n`;
      }
      // 注意：PostgreSQL 没有直接删除注释的语法，如果要删除注释需要设置为 NULL 或空字符串
      // 但通常我们不需要显式删除注释
    }

    return {
      type: MigrationType.MODIFY_COLUMN,
      tableName,
      columnName: newColumn.name,
      sql: sql.trim(),
      description: `修改表 ${tableName} 中的字段 ${newColumn.name}`
    };
  }

  generateDropColumn(tableName: string, columnName: string): MigrationOperation {
    const sql = `ALTER TABLE "public"."${tableName}" DROP COLUMN "${columnName}";`;

    return {
      type: MigrationType.DROP_COLUMN,
      tableName,
      columnName,
      sql,
      description: `删除表 ${tableName} 中的字段 ${columnName}`
    };
  }

  generateAddIndex(tableName: string, index: IndexInfo): MigrationOperation {
    const indexType = index.unique ? 'UNIQUE INDEX' : 'INDEX';
    const indexColumns = index.columns.map(col => `"${col}"`).join(', ');
    const sql = `CREATE ${indexType} "${index.name}" ON "public"."${tableName}" (${indexColumns});`;

    return {
      type: MigrationType.ADD_INDEX,
      tableName,
      indexName: index.name,
      sql,
      description: `在表 ${tableName} 上创建索引 ${index.name}`
    };
  }

  generateDropIndex(tableName: string, indexName: string): MigrationOperation {
    const sql = `DROP INDEX "public"."${indexName}";`;

    return {
      type: MigrationType.DROP_INDEX,
      tableName,
      indexName,
      sql,
      description: `删除索引 ${indexName}`
    };
  }

  generateAddPrimaryKey(tableName: string, columns: string[]): MigrationOperation {
    const pkColumns = columns.map(col => `"${col}"`).join(', ');
    const sql = `ALTER TABLE "public"."${tableName}" ADD PRIMARY KEY (${pkColumns});`;

    return {
      type: MigrationType.ADD_PRIMARY_KEY,
      tableName,
      sql,
      description: `在表 ${tableName} 上添加主键 (${columns.join(', ')})`
    };
  }

  generateDropPrimaryKey(tableName: string): MigrationOperation {
    const sql = `ALTER TABLE "public"."${tableName}" DROP CONSTRAINT "${tableName}_pkey";`;

    return {
      type: MigrationType.DROP_PRIMARY_KEY,
      tableName,
      sql,
      description: `删除表 ${tableName} 的主键`
    };
  }

  generateInsertData(tableName: string, data: Record<string, any>[]): MigrationOperation {
    if (data.length === 0) {
      return {
        type: MigrationType.INSERT_DATA,
        tableName,
        sql: '',
        description: `表 ${tableName} 无数据需要插入`
      };
    }

    const columns = Object.keys(data[0]);
    const columnList = columns.map(col => `"${col}"`).join(', ');

    const values = data.map(row => {
      const rowValues = columns.map(col => this.formatValue(row[col])).join(', ');
      return `(${rowValues})`;
    }).join(',\n  ');

    const sql = `INSERT INTO "public"."${tableName}" (${columnList}) VALUES\n  ${values};`;

    return {
      type: MigrationType.INSERT_DATA,
      tableName,
      sql,
      description: `向表 ${tableName} 插入 ${data.length} 条数据`
    };
  }

  /**
   * 生成列注释语句
   */
  private generateColumnComments(tableName: string, columns: ColumnInfo[]): string[] {
    const commentStatements: string[] = [];
    
    for (const column of columns) {
      if (column.comment) {
        const escapedComment = column.comment.replace(/'/g, "''");
        commentStatements.push(`COMMENT ON COLUMN "public"."${tableName}"."${column.name}" IS '${escapedComment}';`);
      }
    }
    
    return commentStatements;
  }

  private formatColumnDefinition(column: ColumnInfo): string {
    let def = `"${column.name}" ${this.convertToPostgreSQLType(column.type)}`;

    if (!column.nullable) {
      def += ' NOT NULL';
    }

    if (column.defaultValue !== undefined && column.defaultValue !== null) {
      def += ` DEFAULT ${this.formatValue(column.defaultValue)}`;
    }

    return def;
  }

  /**
   * 将数据类型转换为 PostgreSQL 原生类型
   */
  private convertToPostgreSQLType(type: string): string {
    // 移除 MySQL 风格的精度和标度
    let pgType = type.toLowerCase();

    // 处理常见的类型转换为 PostgreSQL 原生类型
    // 注意：bigint 要在 integer 之前检查，因为 bigint 也包含 int
    if (pgType.includes('bigint')) {
      return 'int8';
    }

    if (pgType.includes('integer') || pgType.includes('int(')) {
      return 'int4';
    }

    if (pgType.includes('smallint')) {
      return 'int2';
    }

    if (pgType.includes('character varying') || pgType.includes('varchar')) {
      // 保留长度信息，使用 varchar 格式
      const lengthMatch = pgType.match(/\((\d+)\)/);
      if (lengthMatch) {
        return `varchar(${lengthMatch[1]}) COLLATE "pg_catalog"."default"`;
      }
      return 'varchar COLLATE "pg_catalog"."default"';
    }

    if (pgType.includes('text')) {
      return 'text COLLATE "pg_catalog"."default"';
    }

    if (pgType.includes('timestamp with time zone')) {
      // 检查是否有精度信息
      const precisionMatch = pgType.match(/timestamp.*\((\d+)\)/);
      if (precisionMatch) {
        return `timestamptz(${precisionMatch[1]})`;
      }
      return 'timestamptz(6)'; // 默认精度为6
    }

    if (pgType.includes('timestamp')) {
      // 检查是否有精度信息
      const precisionMatch = pgType.match(/timestamp.*\((\d+)\)/);
      if (precisionMatch) {
        return `timestamp(${precisionMatch[1]})`;
      }
      return 'timestamp(6)'; // 默认精度为6
    }

    if (pgType.includes('boolean')) {
      return 'bool';
    }

    if (pgType.includes('jsonb')) {
      return 'jsonb';
    }

    if (pgType.includes('json')) {
      return 'json';
    }

    if (pgType.includes('geometry')) {
      // 保留 geometry 的完整定义，如 geometry(POINT, 4326)
      if (pgType.includes('(')) {
        // 保持原始大小写格式
        return type; // 返回原始类型而不是小写版本
      }
      return 'geometry';
    }

    if (pgType.includes('numeric') || pgType.includes('decimal')) {
      // 保留精度信息
      const precisionMatch = pgType.match(/\((\d+),?(\d*)\)/);
      if (precisionMatch) {
        return precisionMatch[2] ? `numeric(${precisionMatch[1]},${precisionMatch[2]})` : `numeric(${precisionMatch[1]})`;
      }
      return 'numeric';
    }

    if (pgType.includes('real')) {
      return 'float4';
    }

    if (pgType.includes('double')) {
      return 'float8';
    }

    // 默认返回原类型（去掉精度信息）
    return pgType.replace(/\(\d+,?\d*\)/g, '');
  }

  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'NULL';
    }
    if (typeof value === 'string') {
      // 处理特殊的字符串值
      if (value === 'true') {
        return 'true';
      }
      if (value === 'false') {
        return 'false';
      }
      // 处理复杂的默认值表达式
      if (value.includes('::')) {
        return value; // 保持原样，如 '''0''::character varying'
      }
      return `'${value.replace(/'/g, "''")}'`;
    }
    if (typeof value === 'boolean') {
      return value ? 'true' : 'false';
    }
    if (typeof value === 'number') {
      return String(value);
    }
    return String(value);
  }
}

/**
 * 创建 SQL 生成器
 */
export function createSQLGenerator(driverType: string): SQLGenerator {
  switch (driverType) {
    case 'mysql':
      return new MySQLGenerator();
    case 'postgresql':
    case 'postgres':
      return new PostgreSQLGenerator();
    default:
      throw new Error(`不支持的数据库驱动: ${driverType}`);
  }
}
