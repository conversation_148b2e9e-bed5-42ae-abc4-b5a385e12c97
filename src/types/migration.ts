// 数据库迁移相关类型定义

// 数据库表信息
export interface TableInfo {
  name: string;
  columns: ColumnInfo[];
  indexes: IndexInfo[];
  primaryKey?: string[];
}

// 字段信息
export interface ColumnInfo {
  name: string;
  type: string;
  length?: number;
  precision?: number;
  scale?: number;
  nullable: boolean;
  defaultValue?: string;
  autoIncrement?: boolean;
  comment?: string;
}

// 索引信息
export interface IndexInfo {
  name: string;
  columns: string[];
  unique: boolean;
  type: 'BTREE' | 'HASH' | 'FULLTEXT' | 'SPATIAL';
}

// 数据库结构
export interface DatabaseSchema {
  tables: TableInfo[];
}

// 迁移操作类型
export enum MigrationType {
  CREATE_TABLE = 'CREATE_TABLE',
  ALTER_TABLE = 'ALTER_TABLE',
  DROP_TABLE = 'DROP_TABLE',
  ADD_COLUMN = 'ADD_COLUMN',
  MODIFY_COLUMN = 'MODIFY_COLUMN',
  DROP_COLUMN = 'DROP_COLUMN',
  ADD_INDEX = 'ADD_INDEX',
  DROP_INDEX = 'DROP_INDEX',
  ADD_PRIMARY_KEY = 'ADD_PRIMARY_KEY',
  DROP_PRIMARY_KEY = 'DROP_PRIMARY_KEY',
  INSERT_DATA = 'INSERT_DATA',
  UPDATE_DATA = 'UPDATE_DATA'
}

// 迁移操作
export interface MigrationOperation {
  type: MigrationType;
  tableName: string;
  sql: string;
  description: string;
  columnName?: string;
  indexName?: string;
}

// 迁移文件
export interface MigrationFile {
  timestamp: string;
  sourceDb: string;
  targetDb: string;
  operations: MigrationOperation[];
  filename: string;
}

// 迁移比较结果
export interface MigrationDiff {
  newTables: TableInfo[];
  modifiedTables: TableModification[];
  droppedTables: string[];
  dataChanges: DataChange[];
}

// 表修改信息
export interface TableModification {
  tableName: string;
  newColumns: ColumnInfo[];
  modifiedColumns: ColumnModification[];
  droppedColumns: string[];
  newIndexes: IndexInfo[];
  droppedIndexes: string[];
  primaryKeyChange?: PrimaryKeyChange;
}

// 主键变更信息
export interface PrimaryKeyChange {
  type: 'ADD' | 'DROP' | 'MODIFY';
  oldPrimaryKey?: string[];
  newPrimaryKey?: string[];
}

// 字段修改信息
export interface ColumnModification {
  columnName: string;
  oldColumn: ColumnInfo;
  newColumn: ColumnInfo;
}

// 数据变更信息
export interface DataChange {
  tableName: string;
  type: 'INSERT' | 'UPDATE';
  data: Record<string, any>[];
  condition?: string;
}

// 迁移配置
export interface MigrationConfig {
  sourceDb: string;
  targetDb: string;
  outputDir: string;
  includeData: boolean;
  compareData: boolean;
  excludeTables?: string[];
  includeTables?: string[];
}

// 数据库元数据查询接口
export interface DatabaseMetadata {
  getTables(): Promise<string[]>;
  getTableInfo(tableName: string): Promise<TableInfo>;
  getTableData(tableName: string, limit?: number): Promise<Record<string, any>[]>;
  getRowCount(tableName: string): Promise<number>;
  close(): Promise<void>;
}
