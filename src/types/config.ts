// 数据库配置类型定义
export interface DatabaseConfig {
  driver: string;
  source?: string;
  host?: string;
  port?: number;
  db?: string;
  user?: string;
  password?: string;
  charset?: string;
  // 表过滤配置
  excludeTables?: string[];
  includeTables?: string[];
}

// 应用配置类型定义
export interface Config {
  [key: string]: DatabaseConfig;
}

// 查询结果类型定义
export interface QueryResult {
  columns: string[];
  rows: string[][];
  error?: string;
}

// 视图状态枚举
export enum ViewState {
  DB_SELECTOR = 'dbSelector',
  QUERY_INPUT = 'queryInput',
  RESULT = 'result',
  COLUMN_DETAIL = 'columnDetail'
}

// 焦点状态枚举
export enum FocusState {
  DB_SELECTOR = 'dbSelector',
  QUERY_INPUT = 'queryInput',
  RESULT_TABLE = 'resultTable'
}

// 应用状态接口
export interface AppState {
  config: Config;
  currentDB: string;
  dbNames: string[];
  selectedDB: number;
  dbSelected: boolean;
  viewState: ViewState;
  focusState: FocusState;
  queryText: string;
  queryResult: QueryResult | null;
  showResults: boolean;
  executing: boolean;
  error: string | null;
  statusMsg: string;
  selectedColumn: number;
  selectedRow: number;
  columnDetailValue: string;
  columnDetailName: string;
  currentPage: number;
}
