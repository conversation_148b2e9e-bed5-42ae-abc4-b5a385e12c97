#!/usr/bin/env node

import { loadConfig, getDatabase } from '../utils/config.js';
import { migrate } from '../migration/migrator.js';
import * as path from 'path';
import * as fs from 'fs';

/**
 * 显示迁移帮助信息
 */
function showMigrateHelp() {
  console.log(`
数据库迁移工具 (dbtool migrate)

用法:
  dbtool migrate [选项] <源数据库> <目标数据库> <输出目录>

参数:
  <源数据库>     源数据库配置名称（在 config.yaml 中定义）
  <目标数据库>   目标数据库配置名称（在 config.yaml 中定义）
  <输出目录>     迁移文件输出目录路径

选项:
  -h, --help           显示帮助信息
  -c, --config <path>  指定配置文件路径（默认: ./config.yaml）
  --include-data       包含数据迁移（默认: 仅结构）
  --compare-data       比较数据差异
  --exclude <tables>   排除指定表（逗号分隔，会覆盖配置文件中的设置）
  --include <tables>   仅包含指定表（逗号分隔，会覆盖配置文件中的设置）

表过滤说明:
  1. 可以在配置文件中为每个数据库设置 excludeTables 和 includeTables
  2. 命令行参数 --exclude 和 --include 会覆盖配置文件中的设置
  3. includeTables 优先级更高：如果指定了 includeTables，则只处理这些表
  4. excludeTables 在 includeTables 之后应用：从包含的表中排除指定的表
  5. 表名匹配是精确匹配，区分大小写

示例:
  # 基本迁移（仅结构）
  dbtool migrate db1 db2 ./migrations

  # 包含数据迁移
  dbtool migrate --include-data db1 db2 ./migrations

  # 指定配置文件
  dbtool migrate -c /path/to/config.yaml db1 db2 ./migrations

  # 排除特定表（覆盖配置文件设置）
  dbtool migrate --exclude logs,temp_table,cache_data db1 db2 ./migrations

  # 仅迁移核心表（覆盖配置文件设置）
  dbtool migrate --include users,products,orders db1 db2 ./migrations

  # 同时使用包含和排除（先包含，后排除）
  dbtool migrate --include users,products,orders,logs --exclude logs db1 db2 ./migrations

配置文件示例 (config.yaml):
  source_db:
    driver: mysql
    host: localhost
    port: 3306
    user: root
    password: password
    db: source_database
    # 在配置文件中指定要排除的表
    excludeTables:
      - temp_table
      - log_table
      - cache_table
    # 或者指定要包含的表
    # includeTables:
    #   - users
    #   - products
    #   - orders

  target_db:
    driver: postgresql
    host: localhost
    port: 5432
    user: postgres
    password: password
    db: target_database
    # 目标数据库也可以有自己的表过滤配置
    excludeTables:
      - spatial_ref_sys  # PostGIS 系统表
      - geometry_columns

注意事项:
  1. 迁移前请备份目标数据库
  2. 建议先在测试环境验证迁移脚本
  3. 跨数据库类型迁移需要特别注意数据类型兼容性
  4. 生成的 SQL 文件需要人工审核后再执行
  5. 表过滤会同时应用于结构比较和数据比较
  6. 如果源库和目标库的表过滤配置不同，以各自的配置为准
`);
}

/**
 * 解析命令行参数
 */
function parseArgs(args: string[]): {
  configPath?: string;
  sourceDb?: string;
  targetDb?: string;
  outputDir?: string;
  includeData: boolean;
  compareData: boolean;
  excludeTables?: string[];
  includeTables?: string[];
  showHelp: boolean;
} {
  const result: {
    configPath?: string;
    sourceDb?: string;
    targetDb?: string;
    outputDir?: string;
    includeData: boolean;
    compareData: boolean;
    excludeTables?: string[];
    includeTables?: string[];
    showHelp: boolean;
  } = {
    includeData: false,
    compareData: false,
    showHelp: false
  };

  let i = 0;
  while (i < args.length) {
    const arg = args[i];

    switch (arg) {
      case '-h':
      case '--help':
        result.showHelp = true;
        break;

      case '-c':
      case '--config':
        if (i + 1 < args.length) {
          result.configPath = args[i + 1];
          i++;
        } else {
          throw new Error('--config 选项需要指定配置文件路径');
        }
        break;

      case '--include-data':
        result.includeData = true;
        break;

      case '--compare-data':
        result.compareData = true;
        break;

      case '--exclude':
        if (i + 1 < args.length) {
          result.excludeTables = args[i + 1].split(',').map(t => t.trim());
          i++;
        } else {
          throw new Error('--exclude 选项需要指定表名列表');
        }
        break;

      case '--include':
        if (i + 1 < args.length) {
          result.includeTables = args[i + 1].split(',').map(t => t.trim());
          i++;
        } else {
          throw new Error('--include 选项需要指定表名列表');
        }
        break;

      default:
        // 位置参数
        if (!result.sourceDb) {
          result.sourceDb = arg;
        } else if (!result.targetDb) {
          result.targetDb = arg;
        } else if (!result.outputDir) {
          result.outputDir = arg;
        } else {
          throw new Error(`未知参数: ${arg}`);
        }
        break;
    }

    i++;
  }

  return result;
}

/**
 * 查找配置文件
 */
function findConfigFile(configPath?: string): string {
  if (configPath) {
    if (!fs.existsSync(configPath)) {
      throw new Error(`配置文件不存在: ${configPath}`);
    }
    return configPath;
  }

  const possiblePaths = [
    './config.yaml',
    './config.yml',
    path.join(process.cwd(), 'config.yaml'),
    path.join(process.cwd(), 'config.yml'),
    path.join(process.env.HOME || '~', '.dbtool', 'config.yaml'),
    path.join(process.env.HOME || '~', '.dbtool', 'config.yml')
  ];

  for (const configPath of possiblePaths) {
    if (fs.existsSync(configPath)) {
      return configPath;
    }
  }

  throw new Error('未找到配置文件。请在当前目录或 ~/.dbtool/ 目录下创建 config.yaml 文件');
}

/**
 * 执行迁移命令
 */
export async function runMigrateCommand(args: string[]): Promise<void> {
  try {
    // 解析参数
    const options = parseArgs(args);

    // 显示帮助
    if (options.showHelp) {
      showMigrateHelp();
      return;
    }

    // 验证必需参数
    if (!options.sourceDb || !options.targetDb || !options.outputDir) {
      console.error('❌ 错误: 缺少必需参数');
      console.error('用法: dbtool migrate <源数据库> <目标数据库> <输出目录>');
      console.error('使用 -h 或 --help 查看详细帮助');
      process.exit(1);
    }

    // 查找配置文件
    const configPath = findConfigFile(options.configPath);
    console.log(`📁 使用配置文件: ${configPath}`);

    // 加载配置
    const config = loadConfig(configPath);

    // 获取数据库配置
    const sourceConfig = getDatabase(config, options.sourceDb);
    if (!sourceConfig) {
      throw new Error(`源数据库配置不存在: ${options.sourceDb}`);
    }

    const targetConfig = getDatabase(config, options.targetDb);
    if (!targetConfig) {
      throw new Error(`目标数据库配置不存在: ${options.targetDb}`);
    }

    console.log(`🚀 开始迁移: ${options.sourceDb} -> ${options.targetDb}`);
    console.log(`📂 输出目录: ${options.outputDir}`);

    // 执行迁移
    const result = await migrate(sourceConfig, targetConfig, options.outputDir, {
      includeData: options.includeData,
      compareData: options.compareData,
      excludeTables: options.excludeTables,
      includeTables: options.includeTables
    });

    // 显示结果
    if (result.filePath) {
      console.log(`\n📄 迁移文件: ${result.filePath}`);
      console.log('\n⚠️  重要提醒:');
      console.log('1. 请在执行前备份目标数据库');
      console.log('2. 请仔细检查生成的 SQL 语句');
      console.log('3. 建议在测试环境中先验证迁移脚本');
    }

    console.log('\n🎉 迁移操作已完成！');
    process.exit(0);

  } catch (error) {
    console.error(`❌ 迁移失败: ${error instanceof Error ? error.message : error}`);
    process.exit(1);
  }
}
