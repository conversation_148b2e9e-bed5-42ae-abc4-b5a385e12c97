import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'yaml';
import { Config, DatabaseConfig } from '../types/config.js';

/**
 * 加载配置文件
 */
export function loadConfig(configPath: string): Config {
  try {
    const configFile = path.resolve(configPath);
    const data = fs.readFileSync(configFile, 'utf8');
    const config = yaml.parse(data) as Config;

    // 验证每个数据库配置
    for (const [name, dbConfig] of Object.entries(config)) {
      try {
        validateDatabaseConfig(dbConfig);
      } catch (error) {
        throw new Error(`数据库配置 "${name}" 验证失败: ${error instanceof Error ? error.message : error}`);
      }
    }

    return config;
  } catch (error) {
    throw new Error(`读取配置文件失败: ${error instanceof Error ? error.message : error}`);
  }
}

/**
 * 获取数据库连接字符串
 */
export function getConnectionString(dbConfig: DatabaseConfig): string {
  // 如果直接配置了source，则使用source
  if (dbConfig.source) {
    return dbConfig.source;
  }

  // 否则根据其他字段构建连接字符串
  switch (dbConfig.driver) {
    case 'mysql':
      const charset = dbConfig.charset || 'utf8mb4';
      const port = dbConfig.port || 3306;

      if (!dbConfig.user || !dbConfig.password || !dbConfig.db || !dbConfig.host) {
        throw new Error('MySQL 配置缺少必要参数: host, user, password, db');
      }

      // 使用与 Go 版本相同的连接字符串格式
      return `${dbConfig.user}:${dbConfig.password}@tcp(${dbConfig.host}:${port})/${dbConfig.db}?charset=${charset}&parseTime=True&loc=Local`;

    case 'postgresql':
    case 'postgres':
      const pgPort = dbConfig.port || 5432;

      if (!dbConfig.user || !dbConfig.password || !dbConfig.db || !dbConfig.host) {
        throw new Error('PostgreSQL 配置缺少必要参数: host, user, password, db');
      }

      // 使用与 MySQL 相同的连接字符串格式
      return `${dbConfig.user}:${dbConfig.password}@tcp(${dbConfig.host}:${pgPort})/${dbConfig.db}`;

    default:
      throw new Error(`不支持的数据库驱动: ${dbConfig.driver}`);
  }
}

/**
 * 验证数据库配置
 */
export function validateDatabaseConfig(dbConfig: DatabaseConfig): void {
  if (!dbConfig.driver) {
    throw new Error('数据库驱动不能为空');
  }

  // 如果没有source，则检查其他必需字段
  if (!dbConfig.source) {
    if (!dbConfig.host) {
      throw new Error('主机地址不能为空');
    }
    if (!dbConfig.user) {
      throw new Error('用户名不能为空');
    }
    if (!dbConfig.db) {
      throw new Error('数据库名不能为空');
    }
  }
}

/**
 * 获取数据库名称列表
 */
export function getDatabaseNames(config: Config): string[] {
  return Object.keys(config);
}

/**
 * 获取指定数据库配置
 */
export function getDatabase(config: Config, name: string): DatabaseConfig | null {
  return config[name] || null;
}
