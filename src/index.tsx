#!/usr/bin/env node

import React from 'react';
import { render } from 'ink';
import { App } from './components/App.js';
import { loadConfig } from './utils/config.js';
import { runMigrateCommand } from './cli/migrate.js';
import * as path from 'path';
import * as fs from 'fs';

/**
 * 查找配置文件
 */
function findConfigFile(): string {
  const possiblePaths = [
    './config.yaml',
    './config.yml',
    path.join(process.cwd(), 'config.yaml'),
    path.join(process.cwd(), 'config.yml'),
    path.join(process.env.HOME || '~', '.dbtool', 'config.yaml'),
    path.join(process.env.HOME || '~', '.dbtool', 'config.yml')
  ];

  for (const configPath of possiblePaths) {
    if (fs.existsSync(configPath)) {
      return configPath;
    }
  }

  throw new Error('未找到配置文件。请在当前目录或 ~/.dbtool/ 目录下创建 config.yaml 文件');
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
数据库工具 (dbtool)

用法:
  dbtool [子命令] [选项]

子命令:
  query          启动交互式查询界面（默认）
  migrate        数据库迁移工具

选项:
  -h, --help     显示帮助信息
  -c, --config   指定配置文件路径

子命令帮助:
  dbtool migrate --help    查看迁移工具帮助

配置文件示例 (config.yaml):
  mysql_local:
    driver: mysql
    host: localhost
    port: 3306
    user: root
    password: password
    db: test
    charset: utf8mb4

  mysql_remote:
    driver: mysql
    source: "root:password@tcp(localhost:3306)/database?charset=utf8mb4"

  postgres_local:
    driver: postgresql
    host: localhost
    port: 5432
    user: postgres
    password: password
    db: test

  postgres_remote:
    driver: postgresql
    source: "postgres:password@tcp(localhost:5432)/database"

快捷键:
  Tab           切换焦点 (数据库选择 -> 查询输入 -> 结果表格)
  ↑↓ / j k      上下导航
  ←→ / h l      左右导航 (在结果表格中)
  Enter         确认选择 / 执行查询
  v             查看列详情 (在结果表格中)
  ESC           返回上一级
  Ctrl+C        退出程序
`);
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);

  // 检查是否有子命令
  if (args.length > 0 && !args[0].startsWith('-')) {
    const subCommand = args[0];
    const subArgs = args.slice(1);

    switch (subCommand) {
      case 'migrate':
        await runMigrateCommand(subArgs);
        return;
      case 'query':
        // 默认行为，启动 TUI
        break;
      default:
        console.error(`错误: 未知子命令 ${subCommand}`);
        console.error('可用子命令: query, migrate');
        console.error('使用 dbtool --help 查看帮助');
        process.exit(1);
    }
  }

  // 处理命令行参数（用于 query 子命令或默认行为）
  if (args.includes('-h') || args.includes('--help')) {
    showHelp();
    process.exit(0);
  }

  let configPath: string;
  const configIndex = args.findIndex(arg => arg === '-c' || arg === '--config');

  if (configIndex !== -1 && args[configIndex + 1]) {
    configPath = args[configIndex + 1];
  } else {
    try {
      configPath = findConfigFile();
    } catch (error) {
      console.error(`错误: ${error instanceof Error ? error.message : error}`);
      console.log('\n使用 --help 查看帮助信息');
      process.exit(1);
    }
  }

  // 加载配置
  try {
    const config = loadConfig(configPath);

    if (Object.keys(config).length === 0) {
      console.error('错误: 配置文件中没有找到数据库配置');
      process.exit(1);
    }

    // 启动应用
    render(<App config={config} />);

  } catch (error) {
    console.error(`错误: ${error instanceof Error ? error.message : error}`);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

// 启动应用
main().catch((error) => {
  console.error('应用启动失败:', error);
  process.exit(1);
});
