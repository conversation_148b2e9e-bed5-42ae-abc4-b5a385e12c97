import React from 'react';
import { Box, Text } from 'ink';
import TextInput from 'ink-text-input';
import chalk from 'chalk';

interface QueryInputProps {
  currentDB: string;
  queryText: string;
  onQueryChange: (value: string) => void;
  onSubmit: () => void;
  focused: boolean;
  executing: boolean;
}

export const QueryInput: React.FC<QueryInputProps> = ({
  currentDB,
  queryText,
  onQueryChange,
  onSubmit,
  focused,
  executing
}) => {
  return (
    <Box flexDirection="column" padding={1}>
      {/* 数据库信息显示 */}
      {currentDB && (
        <Box marginBottom={1}>
          <Box 
            borderStyle="round" 
            borderColor="white"
            paddingX={2}
            paddingY={0}
          >
            <Text bold color="black" backgroundColor="white">
              📊 当前数据库: {currentDB}
            </Text>
          </Box>
        </Box>
      )}

      {/* SQL 输入框 */}
      <Box 
        borderStyle="round" 
        borderColor={focused ? "cyan" : "gray"}
        paddingX={1}
        paddingY={0}
      >
        <Box>
          <Text bold color={focused ? "cyan" : "white"}>
            {focused ? "▶ " : "▷ "}
          </Text>
          {focused ? (
            <TextInput
              value={queryText}
              placeholder="输入SQL查询语句..."
              onChange={onQueryChange}
              onSubmit={onSubmit}
              focus={true}
            />
          ) : (
            <Text color="gray">
              {queryText || "输入SQL查询语句..."}
            </Text>
          )}
        </Box>
      </Box>

      {/* 执行状态提示 */}
      {executing && (
        <Box marginTop={1}>
          <Text color="yellow">
            ⏳ 正在执行查询...
          </Text>
        </Box>
      )}


    </Box>
  );
};
