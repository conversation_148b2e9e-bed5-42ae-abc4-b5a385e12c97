import React from 'react';
import { Box, Text } from 'ink';
import SelectInput from 'ink-select-input';
import chalk from 'chalk';

interface DatabaseSelectorProps {
  databases: string[];
  selectedIndex: number;
  onSelect: (item: { label: string; value: string }) => void;
  focused: boolean;
}

export const DatabaseSelector: React.FC<DatabaseSelectorProps> = ({
  databases,
  selectedIndex,
  onSelect,
  focused
}) => {
  const items = databases.map(db => ({
    label: db,
    value: db
  }));

  return (
    <Box flexDirection="column" padding={1}>
      <Box marginBottom={1}>
        <Text bold color="white">
          📊 选择数据库
        </Text>
      </Box>
      
      <Box 
        borderStyle="round" 
        borderColor={focused ? "cyan" : "gray"}
        padding={1}
      >
        {focused ? (
          <SelectInput
            items={items}
            onSelect={onSelect}
            initialIndex={selectedIndex}
          />
        ) : (
          <Box flexDirection="column">
            {databases.map((db, index) => (
              <Box key={db} marginY={0}>
                <Text color={index === selectedIndex ? "white" : "gray"}>
                  {index === selectedIndex ? "▶ " : "  "}
                  {db}
                </Text>
              </Box>
            ))}
          </Box>
        )}
      </Box>
      
      {focused && (
        <Box marginTop={1}>
          <Text color="gray" italic>
            使用 ↑↓ 选择数据库，Enter 确认
          </Text>
        </Box>
      )}
    </Box>
  );
};
