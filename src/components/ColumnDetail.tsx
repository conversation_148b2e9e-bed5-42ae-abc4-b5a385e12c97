import React from 'react';
import { Box, Text } from 'ink';

interface ColumnDetailProps {
  columnName: string;
  columnValue: string;
  onBack: () => void;
}

export const ColumnDetail: React.FC<ColumnDetailProps> = ({
  columnName,
  columnValue,
  onBack
}) => {
  // 文本换行处理
  const wrapText = (text: string, width: number): string[] => {
    if (!text) return [''];
    
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      if (currentLine.length + word.length + 1 <= width) {
        currentLine = currentLine ? `${currentLine} ${word}` : word;
      } else {
        if (currentLine) {
          lines.push(currentLine);
        }
        currentLine = word;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines.length > 0 ? lines : [''];
  };

  const maxWidth = 80; // 最大显示宽度
  const wrappedLines = wrapText(columnValue, maxWidth);

  return (
    <Box flexDirection="column" padding={1}>
      {/* 标题 */}
      <Box marginBottom={1}>
        <Text bold color="white">
          🔍 列详情: {columnName}
        </Text>
      </Box>

      {/* 内容框 */}
      <Box 
        borderStyle="round" 
        borderColor="cyan"
        padding={2}
        flexDirection="column"
        minHeight={10}
      >
        {wrappedLines.map((line, index) => (
          <Box key={index}>
            <Text>{line}</Text>
          </Box>
        ))}
      </Box>

      {/* 帮助信息 */}
      <Box marginTop={1}>
        <Box 
          borderStyle="round" 
          borderColor="gray"
          paddingX={1}
        >
          <Text color="gray" italic>
            ESC: 返回表格视图 | Ctrl+C: 退出
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
