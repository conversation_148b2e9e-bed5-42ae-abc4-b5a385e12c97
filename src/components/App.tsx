import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput, useApp, useStdout } from 'ink';
import { Config, ViewState, FocusState, QueryResult, AppState } from '../types/config.js';
import { DatabaseSelector } from './DatabaseSelector.js';
import { QueryInput } from './QueryInput.js';
import { ResultTable } from './ResultTable.js';
import { ColumnDetail } from './ColumnDetail.js';
import { getDriver } from '../database/driver.js';
import { getDatabase } from '../utils/config.js';

interface AppProps {
  config: Config;
}

export const App: React.FC<AppProps> = ({ config }) => {
  const { exit } = useApp();
  const { stdout } = useStdout();

  // 获取终端尺寸
  const [terminalSize, setTerminalSize] = useState({
    width: stdout?.columns || 120,
    height: stdout?.rows || 30
  });

  // 监听终端尺寸变化
  useEffect(() => {
    const updateSize = () => {
      setTerminalSize({
        width: stdout?.columns || 120,
        height: stdout?.rows || 30
      });
    };

    if (stdout) {
      stdout.on('resize', updateSize);
      return () => {
        stdout.off('resize', updateSize);
      };
    }
  }, [stdout]);

  // 初始化状态
  const dbNames = Object.keys(config);
  const [state, setState] = useState<AppState>({
    config,
    currentDB: dbNames.length > 0 ? dbNames[0] : '',
    dbNames,
    selectedDB: 0,
    dbSelected: dbNames.length > 0,
    viewState: dbNames.length > 0 ? ViewState.QUERY_INPUT : ViewState.DB_SELECTOR,
    focusState: dbNames.length > 0 ? FocusState.QUERY_INPUT : FocusState.DB_SELECTOR,
    queryText: '',
    queryResult: null,
    showResults: false,
    executing: false,
    error: null,
    statusMsg: '',
    selectedColumn: 0,
    selectedRow: 0,
    columnDetailValue: '',
    columnDetailName: '',
    currentPage: 0
  });

  // 执行查询
  const executeQuery = useCallback(async () => {
    if (!state.currentDB || !state.queryText.trim()) {
      setState(prev => ({
        ...prev,
        error: '请选择数据库并输入查询语句'
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      executing: true,
      error: null,
      statusMsg: '正在执行查询...'
    }));

    try {
      const dbConfig = getDatabase(state.config, state.currentDB);
      if (!dbConfig) {
        throw new Error(`数据库配置不存在: ${state.currentDB}`);
      }

      const driver = getDriver(dbConfig.driver);
      const connection = await driver.connect(dbConfig);
      const result = await driver.query(connection, state.queryText.trim());
      await driver.close(connection);

      setState(prev => ({
        ...prev,
        executing: false,
        queryResult: result,
        showResults: true,
        error: result.error || null,
        statusMsg: result.error ? '' : '查询执行成功',
        selectedRow: 0,
        selectedColumn: 0,
        currentPage: 0
        // 保持焦点在查询输入框，不自动切换到结果表格
      }));

    } catch (error) {
      setState(prev => ({
        ...prev,
        executing: false,
        error: error instanceof Error ? error.message : String(error),
        statusMsg: ''
      }));
    }
  }, [state.currentDB, state.queryText, state.config]);

  // 键盘事件处理 - 使用更高优先级，禁用其他组件的输入捕获
  useInput((input, key) => {
    // Tab 键切换焦点 - 优先处理，阻止其他组件处理
    if (key.tab) {
      setState(prev => {
        let newFocus = prev.focusState;
        let newView = prev.viewState;

        switch (prev.focusState) {
          case FocusState.DB_SELECTOR:
            newFocus = FocusState.QUERY_INPUT;
            if (prev.dbSelected) {
              newView = ViewState.QUERY_INPUT;
            }
            break;
          case FocusState.QUERY_INPUT:
            if (prev.showResults) {
              newFocus = FocusState.RESULT_TABLE;
              // 保持当前视图状态，不改变viewState
            } else {
              newFocus = FocusState.DB_SELECTOR;
            }
            break;
          case FocusState.RESULT_TABLE:
            newFocus = FocusState.QUERY_INPUT;
            break;
        }

        return {
          ...prev,
          focusState: newFocus,
          viewState: newView
        };
      });
      return;
    }

    // 全局快捷键
    if (key.ctrl && input === 'c') {
      exit();
      return;
    }

    // ESC 键处理
    if (key.escape) {
      if (state.viewState === ViewState.COLUMN_DETAIL) {
        setState(prev => ({
          ...prev,
          viewState: ViewState.RESULT
        }));
      }
      return;
    }



    // 根据当前焦点处理其他按键
    if (state.focusState === FocusState.DB_SELECTOR) {
      if (key.upArrow || input === 'k') {
        setState(prev => ({
          ...prev,
          selectedDB: (prev.selectedDB - 1 + prev.dbNames.length) % prev.dbNames.length
        }));
      } else if (key.downArrow || input === 'j') {
        setState(prev => ({
          ...prev,
          selectedDB: (prev.selectedDB + 1) % prev.dbNames.length
        }));
      } else if (key.return) {
        setState(prev => ({
          ...prev,
          currentDB: prev.dbNames[prev.selectedDB],
          dbSelected: true,
          focusState: FocusState.QUERY_INPUT,
          viewState: ViewState.QUERY_INPUT
        }));
      }
    } else if (state.focusState === FocusState.RESULT_TABLE && state.queryResult) {
      const { columns, rows } = state.queryResult;
      const ROWS_PER_PAGE = 5;
      const totalPages = Math.ceil(rows.length / ROWS_PER_PAGE);

      if (key.upArrow || input === 'k') {
        setState(prev => {
          let newRow = prev.selectedRow - 1;
          let newPage = prev.currentPage;

          // 如果到达当前页顶部，尝试跳到上一页
          if (newRow < prev.currentPage * ROWS_PER_PAGE && prev.currentPage > 0) {
            newPage = prev.currentPage - 1;
            newRow = (newPage + 1) * ROWS_PER_PAGE - 1; // 上一页的最后一行
          }

          return {
            ...prev,
            selectedRow: Math.max(0, newRow),
            currentPage: newPage
          };
        });
      } else if (key.downArrow || input === 'j') {
        setState(prev => {
          let newRow = prev.selectedRow + 1;
          let newPage = prev.currentPage;

          // 如果到达当前页底部，尝试跳到下一页
          if (newRow >= (prev.currentPage + 1) * ROWS_PER_PAGE && prev.currentPage < totalPages - 1) {
            newPage = prev.currentPage + 1;
            newRow = newPage * ROWS_PER_PAGE; // 下一页的第一行
          }

          return {
            ...prev,
            selectedRow: Math.min(rows.length - 1, newRow),
            currentPage: newPage
          };
        });
      } else if (key.leftArrow || input === 'h') {
        // 向左移动列，支持循环
        setState(prev => {
          const newColumn = prev.selectedColumn - 1;
          return {
            ...prev,
            selectedColumn: newColumn < 0 ? columns.length - 1 : newColumn
          };
        });
      } else if (key.rightArrow || input === 'l') {
        // 向右移动列，支持循环
        setState(prev => {
          const newColumn = prev.selectedColumn + 1;
          return {
            ...prev,
            selectedColumn: newColumn >= columns.length ? 0 : newColumn
          };
        });
      } else if (key.pageUp) {
        // Page Up - 上一页
        setState(prev => {
          const newPage = Math.max(0, prev.currentPage - 1);
          const newRow = Math.min(prev.selectedRow, (newPage + 1) * ROWS_PER_PAGE - 1);
          return {
            ...prev,
            currentPage: newPage,
            selectedRow: Math.max(newPage * ROWS_PER_PAGE, newRow)
          };
        });
      } else if (key.pageDown) {
        // Page Down - 下一页
        setState(prev => {
          const newPage = Math.min(totalPages - 1, prev.currentPage + 1);
          const newRow = Math.max(prev.selectedRow, newPage * ROWS_PER_PAGE);
          return {
            ...prev,
            currentPage: newPage,
            selectedRow: Math.min(rows.length - 1, newRow)
          };
        });
      } else if (input === 'v') {
        const cellValue = rows[state.selectedRow]?.[state.selectedColumn] || '';
        const columnName = columns[state.selectedColumn] || '';
        setState(prev => ({
          ...prev,
          columnDetailValue: cellValue,
          columnDetailName: columnName,
          viewState: ViewState.COLUMN_DETAIL
        }));
      }
    }
  });

  // 数据库选择处理
  const handleDatabaseSelect = useCallback((item: { label: string; value: string }) => {
    setState(prev => ({
      ...prev,
      currentDB: item.value,
      dbSelected: true,
      focusState: FocusState.QUERY_INPUT,
      viewState: ViewState.QUERY_INPUT
    }));
  }, []);

  // 查询文本变化处理
  const handleQueryChange = useCallback((value: string) => {
    setState(prev => ({
      ...prev,
      queryText: value
    }));
  }, []);

  // 列详情返回处理
  const handleColumnDetailBack = useCallback(() => {
    setState(prev => ({
      ...prev,
      viewState: ViewState.RESULT
    }));
  }, []);

  return (
    <Box flexDirection="column">
      {/* 错误信息显示 */}
      {state.error && (
        <Box 
          borderStyle="round" 
          borderColor="red"
          padding={1}
          marginBottom={1}
        >
          <Text color="red">
            ❌ {state.error}
          </Text>
        </Box>
      )}

      {/* 主要内容区域 */}
      {state.viewState === ViewState.COLUMN_DETAIL ? (
        <ColumnDetail
          columnName={state.columnDetailName}
          columnValue={state.columnDetailValue}
          onBack={handleColumnDetailBack}
        />
      ) : (
        <Box flexDirection="column">
          {/* 数据库选择器 - 显示在上面，只在聚焦时显示 */}
          {state.focusState === FocusState.DB_SELECTOR && (
            <DatabaseSelector
              databases={state.dbNames}
              selectedIndex={state.selectedDB}
              onSelect={handleDatabaseSelect}
              focused={state.focusState === FocusState.DB_SELECTOR}
            />
          )}

          {/* 查询输入 - 显示在上面，在聚焦时或有查询结果时显示 */}
          {(state.focusState === FocusState.QUERY_INPUT || state.showResults) && (
            <QueryInput
              currentDB={state.currentDB}
              queryText={state.queryText}
              onQueryChange={handleQueryChange}
              onSubmit={executeQuery}
              focused={state.focusState === FocusState.QUERY_INPUT}
              executing={state.executing}
            />
          )}

          {/* 查询结果 - 显示在下面 */}
          {state.showResults && state.queryResult && !state.queryResult.error && (
            <Box marginTop={1}>
              <ResultTable
                result={state.queryResult}
                selectedRow={state.selectedRow}
                selectedColumn={state.selectedColumn}
                focused={state.focusState === FocusState.RESULT_TABLE}
                onViewColumn={() => {}}
                terminalWidth={terminalSize.width}
                terminalHeight={terminalSize.height}
                currentPage={state.currentPage}
                onPageChange={(page) => setState(prev => ({ ...prev, currentPage: page }))}
              />
            </Box>
          )}
        </Box>
      )}


    </Box>
  );
};
