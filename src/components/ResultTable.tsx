import React, { useMemo } from 'react';
import { Box, Text, useStdout } from 'ink';
import { QueryResult } from '../types/config.js';

interface ResultTableProps {
  result: QueryResult;
  selectedRow: number;
  selectedColumn: number;
  focused: boolean;
  onViewColumn: () => void;
  terminalWidth?: number;
  terminalHeight?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
}

interface TableColumn {
  title: string;
  width: number;
}

export const ResultTable: React.FC<ResultTableProps> = ({
  result,
  selectedRow,
  selectedColumn,
  focused,
  onViewColumn,
  terminalWidth = 120,
  terminalHeight = 30,
  currentPage = 0,
  onPageChange
}) => {
  const { stdout } = useStdout();

  if (!result || result.columns.length === 0) {
    return null;
  }

  const { columns, rows } = result;
  const ROWS_PER_PAGE = 5;

  // 响应式表格计算和分页
  const tableConfig = useMemo(() => {
    const availableWidth = Math.max(terminalWidth - 6, 80); // 减少边距，更紧凑

    // 计算每列的最佳宽度 - 更紧凑的设计
    const columnWidths = columns.map((col, index) => {
      const headerWidth = col.length;
      const dataWidth = Math.max(
        ...rows.slice(0, 100).map(row => (row[index] || '').toString().length)
      );
      return Math.min(Math.max(headerWidth, dataWidth, 6), 20); // 最小6，最大20，更紧凑
    });

    // 计算可见列范围（水平滚动）
    let visibleColumns: TableColumn[] = [];
    let visibleColumnIndices: number[] = [];
    let currentWidth = 0;
    let startIndex = 0;

    // 确保选中的列在可见范围内
    if (selectedColumn >= 0 && selectedColumn < columns.length) {
      // 从选中列开始，向左右扩展
      let leftIndex = selectedColumn;
      let rightIndex = selectedColumn;

      // 先添加选中的列
      visibleColumns.push({
        title: columns[selectedColumn],
        width: columnWidths[selectedColumn]
      });
      visibleColumnIndices.push(selectedColumn);
      currentWidth = columnWidths[selectedColumn] + 1; // 减少间距

      // 向右扩展
      while (rightIndex + 1 < columns.length && currentWidth + columnWidths[rightIndex + 1] + 1 <= availableWidth) {
        rightIndex++;
        visibleColumns.push({
          title: columns[rightIndex],
          width: columnWidths[rightIndex]
        });
        visibleColumnIndices.push(rightIndex);
        currentWidth += columnWidths[rightIndex] + 1;
      }

      // 向左扩展
      while (leftIndex - 1 >= 0 && currentWidth + columnWidths[leftIndex - 1] + 1 <= availableWidth) {
        leftIndex--;
        visibleColumns.unshift({
          title: columns[leftIndex],
          width: columnWidths[leftIndex]
        });
        visibleColumnIndices.unshift(leftIndex);
        currentWidth += columnWidths[leftIndex] + 1;
      }

      startIndex = leftIndex;
    } else {
      // 如果没有选中列，从第一列开始显示
      for (let i = 0; i < columns.length && currentWidth + columnWidths[i] + 1 <= availableWidth; i++) {
        visibleColumns.push({
          title: columns[i],
          width: columnWidths[i]
        });
        visibleColumnIndices.push(i);
        currentWidth += columnWidths[i] + 1;
      }
    }

    // 分页计算
    const totalPages = Math.ceil(rows.length / ROWS_PER_PAGE);
    const startRow = currentPage * ROWS_PER_PAGE;
    const endRow = Math.min(startRow + ROWS_PER_PAGE, rows.length);
    const visibleRows = rows.slice(startRow, endRow);

    return {
      visibleColumns,
      visibleColumnIndices,
      visibleRows,
      startIndex,
      totalColumns: columns.length,
      totalRows: rows.length,
      totalPages,
      currentPage,
      startRow,
      endRow
    };
  }, [columns, rows, selectedColumn, terminalWidth, currentPage]);

  const renderCell = (content: string, width: number, isSelected: boolean = false, isHeader: boolean = false, isRowSelected: boolean = false) => {
    const truncated = content.length > width ? content.slice(0, width - 2) + '..' : content;
    const padded = truncated.padEnd(width);

    if (isHeader) {
      return (
        <Text bold color="black" backgroundColor="white">
          {padded}
        </Text>
      );
    }

    if (isSelected && focused) {
      return (
        <Text backgroundColor="cyan" color="black" bold>
          {padded}
        </Text>
      );
    }

    if (isRowSelected && focused) {
      return (
        <Text backgroundColor="blue" color="white">
          {padded}
        </Text>
      );
    }

    // 根据内容类型设置不同颜色
    if (content === 'NULL' || content === null || content === undefined) {
      return <Text color="gray" dimColor>{padded}</Text>;
    }

    return <Text color="white">{padded}</Text>;
  };

  const renderColumnHighlight = (colIndex: number) => {
    const originalIndex = tableConfig.visibleColumnIndices[colIndex];
    return originalIndex === selectedColumn && focused;
  };

  // 计算当前页的实际行索引
  const getActualRowIndex = (pageRowIndex: number) => {
    return tableConfig.startRow + pageRowIndex;
  };

  return (
    <Box flexDirection="column">
      {/* 表格标题栏 */}
      <Box
        borderStyle="single"
        borderColor={focused ? "cyan" : "gray"}
        paddingX={1}
        marginBottom={0}
      >
        <Text bold color={focused ? "cyan" : "white"}>
          📊 查询结果 ({tableConfig.totalRows} 行)
        </Text>
        {tableConfig.totalPages > 1 && (
          <Box marginLeft={2}>
            <Text color="gray">
              第 {tableConfig.currentPage + 1}/{tableConfig.totalPages} 页
            </Text>
          </Box>
        )}
      </Box>

      {/* 现代化表格 */}
      <Box
        borderStyle="single"
        borderColor={focused ? "cyan" : "gray"}
        borderTop={false}
        flexDirection="column"
      >
        {/* 表头 */}
        <Box>
          {tableConfig.visibleColumns.map((col, index) => (
            <Box key={index} width={col.width} marginRight={index < tableConfig.visibleColumns.length - 1 ? 1 : 0}>
              {renderCell(col.title, col.width, renderColumnHighlight(index), true)}
            </Box>
          ))}
        </Box>

        {/* 数据行 */}
        {tableConfig.visibleRows.map((row, rowIndex) => {
          const actualRowIndex = getActualRowIndex(rowIndex);
          const isRowSelected = actualRowIndex === selectedRow;

          return (
            <Box key={rowIndex}>
              {tableConfig.visibleColumns.map((col, colIndex) => {
                const originalColIndex = tableConfig.visibleColumnIndices[colIndex];
                const cellValue = row[originalColIndex] || 'NULL';
                const isCellSelected = focused &&
                  actualRowIndex === selectedRow &&
                  originalColIndex === selectedColumn;

                return (
                  <Box key={colIndex} width={col.width} marginRight={colIndex < tableConfig.visibleColumns.length - 1 ? 1 : 0}>
                    {renderCell(cellValue, col.width, isCellSelected, false, isRowSelected)}
                  </Box>
                );
              })}
            </Box>
          );
        })}
      </Box>

      {/* 状态栏 */}
      <Box
        borderStyle="single"
        borderColor={focused ? "cyan" : "gray"}
        borderTop={false}
        paddingX={1}
        justifyContent="space-between"
      >
        <Box>
          <Text color="gray">
            行 {selectedRow + 1}/{tableConfig.totalRows}
          </Text>
        </Box>

        {/* 帮助信息 */}
        <Box>
          <Text color="gray" italic>
            ↑↓jk:行 | ←→hl:列 | PgUp/PgDn:翻页 | v:详情 | Tab:切换
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
