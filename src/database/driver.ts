import mysql from 'mysql2/promise';
import pg from 'pg';
import { DatabaseConfig, QueryResult } from '../types/config.js';

/**
 * 数据库驱动接口
 */
export interface DatabaseDriver {
  connect(config: DatabaseConfig): Promise<any>;
  query(connection: any, sql: string): Promise<QueryResult>;
  close(connection: any): Promise<void>;
  test(config: DatabaseConfig): Promise<void>;
}

/**
 * MySQL 驱动实现
 */
export class MySQLDriver implements DatabaseDriver {
  async connect(config: DatabaseConfig): Promise<mysql.Connection> {
    try {
      let connectionOptions: any;

      if (config.source) {
        // 解析 Go 格式的连接字符串
        connectionOptions = this.parseGoConnectionString(config.source);
      } else {
        // 使用详细配置构建连接选项
        connectionOptions = {
          host: config.host || 'localhost',
          port: config.port || 3306,
          user: config.user,
          password: config.password,
          database: config.db,
          charset: config.charset || 'utf8mb4',
          timezone: '+00:00',
          dateStrings: false
        };
      }

      const connection = await mysql.createConnection(connectionOptions);
      return connection;
    } catch (error) {
      throw new Error(`MySQL 连接失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 解析 Go 格式的连接字符串
   * 格式: user:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local
   */
  parseGoConnectionString(source: string): any {
    try {
      // 移除可能的 mysql:// 前缀
      const cleanSource = source.replace(/^mysql:\/\//, '');

      // 解析格式: user:password@tcp(host:port)/database?params
      const match = cleanSource.match(/^([^:]+):([^@]+)@tcp\(([^:]+):(\d+)\)\/([^?]+)(\?(.+))?$/);

      if (!match) {
        throw new Error('无效的连接字符串格式');
      }

      const [, user, password, host, port, database, , params] = match;

      // 解析查询参数
      const queryParams: Record<string, string> = {};
      if (params) {
        params.split('&').forEach(param => {
          const [key, value] = param.split('=');
          if (key && value) {
            queryParams[key] = decodeURIComponent(value);
          }
        });
      }

      return {
        host,
        port: parseInt(port, 10),
        user,
        password,
        database,
        charset: queryParams.charset || 'utf8mb4',
        timezone: '+00:00',
        dateStrings: false
      };
    } catch (error) {
      throw new Error(`解析连接字符串失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  async query(connection: mysql.Connection, sql: string): Promise<QueryResult> {
    try {
      const [rows, fields] = await connection.execute(sql);
      
      // 处理查询结果
      if (Array.isArray(rows)) {
        const columns = fields?.map(field => field.name) || [];
        const resultRows = rows.map((row: any) => {
          if (typeof row === 'object' && row !== null) {
            return columns.map(col => {
              const value = (row as any)[col];
              if (value === null || value === undefined) {
                return 'NULL';
              }
              if (value instanceof Date) {
                return value.toISOString().slice(0, 19).replace('T', ' ');
              }
              if (typeof value === 'boolean') {
                return value ? 'true' : 'false';
              }
              if (typeof value === 'number') {
                return value.toString();
              }
              return String(value);
            });
          }
          return [];
        });

        return {
          columns,
          rows: resultRows
        };
      }

      return {
        columns: [],
        rows: []
      };
    } catch (error) {
      return {
        columns: [],
        rows: [],
        error: `查询执行失败: ${error instanceof Error ? error.message : error}`
      };
    }
  }

  async close(connection: mysql.Connection): Promise<void> {
    try {
      await connection.end();
    } catch (error) {
      // 忽略关闭连接时的错误
    }
  }

  async test(config: DatabaseConfig): Promise<void> {
    let connection: mysql.Connection | null = null;
    try {
      connection = await this.connect(config);
      await connection.ping();
    } catch (error) {
      throw new Error(`数据库连接测试失败: ${error instanceof Error ? error.message : error}`);
    } finally {
      if (connection) {
        await this.close(connection);
      }
    }
  }
}

/**
 * PostgreSQL 驱动实现
 */
export class PostgreSQLDriver implements DatabaseDriver {
  async connect(config: DatabaseConfig): Promise<pg.Client> {
    try {
      let connectionOptions: any;

      if (config.source) {
        // 解析 PostgreSQL 连接字符串
        connectionOptions = this.parsePostgreSQLConnectionString(config.source);
      } else {
        // 使用详细配置构建连接选项
        connectionOptions = {
          host: config.host || 'localhost',
          port: config.port || 5432,
          user: config.user,
          password: config.password ? String(config.password) : undefined, // 确保密码是字符串
          database: config.db,
        };
      }

      const client = new pg.Client(connectionOptions);
      await client.connect();
      return client;
    } catch (error) {
      throw new Error(`PostgreSQL 连接失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 解析 PostgreSQL 连接字符串
   * 支持两种格式:
   * 1. 类似 MySQL 格式: user:password@tcp(host:port)/database?params
   * 2. 标准 PostgreSQL 格式: postgres://user:password@host:port/database
   */
  parsePostgreSQLConnectionString(source: string): any {
    try {
      // 检查是否是类似 MySQL 的格式
      if (source.includes('@tcp(')) {
        // 解析格式: user:password@tcp(host:port)/database?params
        const match = source.match(/^([^:]+):([^@]+)@tcp\(([^:]+):(\d+)\)\/([^?]+)(\?(.+))?$/);

        if (!match) {
          throw new Error('无效的连接字符串格式');
        }

        const [, user, password, host, port, database] = match;

        return {
          host,
          port: parseInt(port, 10),
          user,
          password: String(password), // 确保密码是字符串
          database,
        };
      } else {
        // 标准 PostgreSQL URL 格式
        const url = new URL(source);

        if (!['postgres:', 'postgresql:'].includes(url.protocol)) {
          throw new Error('无效的 PostgreSQL 连接字符串协议');
        }

        return {
          host: url.hostname,
          port: url.port ? parseInt(url.port, 10) : 5432,
          user: url.username,
          password: url.password ? String(url.password) : undefined, // 确保密码是字符串
          database: url.pathname.slice(1), // 移除开头的 '/'
        };
      }
    } catch (error) {
      throw new Error(`解析 PostgreSQL 连接字符串失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  async query(connection: pg.Client, sql: string): Promise<QueryResult> {
    try {
      const result = await connection.query(sql);

      // 处理查询结果
      if (result.rows && Array.isArray(result.rows)) {
        const columns = result.fields?.map(field => field.name) || [];
        const resultRows = result.rows.map((row: any) => {
          return columns.map(col => {
            const value = row[col];
            if (value === null || value === undefined) {
              return 'NULL';
            }
            if (value instanceof Date) {
              return value.toISOString().slice(0, 19).replace('T', ' ');
            }
            if (typeof value === 'boolean') {
              return value ? 'true' : 'false';
            }
            if (typeof value === 'number') {
              return value.toString();
            }
            if (typeof value === 'object') {
              return JSON.stringify(value);
            }
            return String(value);
          });
        });

        return {
          columns,
          rows: resultRows
        };
      }

      return {
        columns: [],
        rows: []
      };
    } catch (error) {
      return {
        columns: [],
        rows: [],
        error: `查询执行失败: ${error instanceof Error ? error.message : error}`
      };
    }
  }

  async close(connection: pg.Client): Promise<void> {
    try {
      await connection.end();
    } catch (error) {
      // 忽略关闭连接时的错误
    }
  }

  async test(config: DatabaseConfig): Promise<void> {
    let connection: pg.Client | null = null;
    try {
      connection = await this.connect(config);
      await connection.query('SELECT 1');
    } catch (error) {
      throw new Error(`PostgreSQL 连接测试失败: ${error instanceof Error ? error.message : error}`);
    } finally {
      if (connection) {
        await this.close(connection);
      }
    }
  }
}

/**
 * 获取数据库驱动实例
 */
export function getDriver(driverName: string): DatabaseDriver {
  switch (driverName) {
    case 'mysql':
      return new MySQLDriver();
    case 'postgresql':
    case 'postgres':
      return new PostgreSQLDriver();
    default:
      throw new Error(`不支持的数据库驱动: ${driverName}`);
  }
}
