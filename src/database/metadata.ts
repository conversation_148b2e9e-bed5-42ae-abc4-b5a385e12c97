import mysql from 'mysql2/promise';
import pg from 'pg';
import { DatabaseMetadata, TableInfo, ColumnInfo, IndexInfo } from '../types/migration.js';
import { DatabaseConfig } from '../types/config.js';
import { getDriver } from './driver.js';

/**
 * MySQL 元数据查询实现
 */
export class MySQLMetadata implements DatabaseMetadata {
  private connection: mysql.Connection;
  private dbName: string;
  private config: DatabaseConfig;

  constructor(connection: mysql.Connection, dbName: string, config: DatabaseConfig) {
    this.connection = connection;
    this.dbName = dbName;
    this.config = config;
  }

  async close(): Promise<void> {
    try {
      await this.connection.end();
    } catch (error) {
      // 忽略关闭连接时的错误
    }
  }

  async getTables(): Promise<string[]> {
    const [rows] = await this.connection.execute(
      'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_TYPE = "BASE TABLE"',
      [this.dbName]
    );
    const allTables = (rows as any[]).map(row => row.TABLE_NAME);
    return this.filterTables(allTables);
  }

  /**
   * 根据配置过滤表列表
   */
  private filterTables(tables: string[]): string[] {
    let filteredTables = tables;

    // 如果指定了包含的表，只保留指定的表
    if (this.config.includeTables && this.config.includeTables.length > 0) {
      filteredTables = filteredTables.filter(table => 
        this.config.includeTables!.includes(table)
      );
    }

    // 排除指定的表
    if (this.config.excludeTables && this.config.excludeTables.length > 0) {
      filteredTables = filteredTables.filter(table => 
        !this.config.excludeTables!.includes(table)
      );
    }

    return filteredTables;
  }

  async getTableInfo(tableName: string): Promise<TableInfo> {
    // 获取字段信息
    const columns = await this.getColumns(tableName);
    
    // 获取索引信息
    const indexes = await this.getIndexes(tableName);
    
    // 获取主键信息
    const primaryKey = await this.getPrimaryKey(tableName);

    return {
      name: tableName,
      columns,
      indexes,
      primaryKey
    };
  }

  private async getColumns(tableName: string): Promise<ColumnInfo[]> {
    const [rows] = await this.connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        CHARACTER_MAXIMUM_LENGTH,
        NUMERIC_PRECISION,
        NUMERIC_SCALE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        EXTRA,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [this.dbName, tableName]);

    return (rows as any[]).map(row => ({
      name: row.COLUMN_NAME,
      type: this.formatMySQLType(row),
      length: row.CHARACTER_MAXIMUM_LENGTH,
      precision: row.NUMERIC_PRECISION,
      scale: row.NUMERIC_SCALE,
      nullable: row.IS_NULLABLE === 'YES',
      defaultValue: row.COLUMN_DEFAULT,
      autoIncrement: row.EXTRA.includes('auto_increment'),
      comment: row.COLUMN_COMMENT
    }));
  }

  private formatMySQLType(row: any): string {
    let type = row.DATA_TYPE.toLowerCase();
    
    if (row.CHARACTER_MAXIMUM_LENGTH) {
      type += `(${row.CHARACTER_MAXIMUM_LENGTH})`;
    } else if (row.NUMERIC_PRECISION && row.NUMERIC_SCALE !== null) {
      type += `(${row.NUMERIC_PRECISION},${row.NUMERIC_SCALE})`;
    } else if (row.NUMERIC_PRECISION) {
      type += `(${row.NUMERIC_PRECISION})`;
    }
    
    return type;
  }

  private async getIndexes(tableName: string): Promise<IndexInfo[]> {
    const [rows] = await this.connection.execute(`
      SELECT 
        INDEX_NAME,
        COLUMN_NAME,
        NON_UNIQUE,
        INDEX_TYPE
      FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY INDEX_NAME, SEQ_IN_INDEX
    `, [this.dbName, tableName]);

    const indexMap = new Map<string, IndexInfo>();
    
    (rows as any[]).forEach(row => {
      if (row.INDEX_NAME === 'PRIMARY') return; // 跳过主键索引
      
      if (!indexMap.has(row.INDEX_NAME)) {
        indexMap.set(row.INDEX_NAME, {
          name: row.INDEX_NAME,
          columns: [],
          unique: row.NON_UNIQUE === 0,
          type: row.INDEX_TYPE as any
        });
      }
      
      indexMap.get(row.INDEX_NAME)!.columns.push(row.COLUMN_NAME);
    });

    return Array.from(indexMap.values());
  }

  private async getPrimaryKey(tableName: string): Promise<string[] | undefined> {
    const [rows] = await this.connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND CONSTRAINT_NAME = 'PRIMARY'
      ORDER BY ORDINAL_POSITION
    `, [this.dbName, tableName]);

    const columns = (rows as any[]).map(row => row.COLUMN_NAME);
    return columns.length > 0 ? columns : undefined;
  }

  async getTableData(tableName: string, limit: number = 1000): Promise<Record<string, any>[]> {
    const [rows] = await this.connection.execute(
      `SELECT * FROM \`${tableName}\` LIMIT ?`,
      [limit]
    );
    return rows as Record<string, any>[];
  }

  async getRowCount(tableName: string): Promise<number> {
    const [rows] = await this.connection.execute(
      `SELECT COUNT(*) as count FROM \`${tableName}\``
    );
    return (rows as any[])[0].count;
  }
}

/**
 * PostgreSQL 元数据查询实现
 */
export class PostgreSQLMetadata implements DatabaseMetadata {
  private client: pg.Client;
  private dbName: string;
  private config: DatabaseConfig;

  constructor(client: pg.Client, dbName: string, config: DatabaseConfig) {
    this.client = client;
    this.dbName = dbName;
    this.config = config;
  }

  async close(): Promise<void> {
    try {
      await this.client.end();
    } catch (error) {
      // 忽略关闭连接时的错误
    }
  }

  async getTables(): Promise<string[]> {
    const result = await this.client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    `);
    const allTables = result.rows.map(row => row.table_name);
    return this.filterTables(allTables);
  }

  /**
   * 根据配置过滤表列表
   */
  private filterTables(tables: string[]): string[] {
    let filteredTables = tables;

    // 如果指定了包含的表，只保留指定的表
    if (this.config.includeTables && this.config.includeTables.length > 0) {
      filteredTables = filteredTables.filter(table => 
        this.config.includeTables!.includes(table)
      );
    }

    // 排除指定的表
    if (this.config.excludeTables && this.config.excludeTables.length > 0) {
      filteredTables = filteredTables.filter(table => 
        !this.config.excludeTables!.includes(table)
      );
    }

    return filteredTables;
  }

  async getTableInfo(tableName: string): Promise<TableInfo> {
    // 获取字段信息
    const columns = await this.getColumns(tableName);

    // 获取索引信息
    const indexes = await this.getIndexes(tableName);

    // 获取主键信息
    const primaryKey = await this.getPrimaryKey(tableName);

    return {
      name: tableName,
      columns,
      indexes,
      primaryKey
    };
  }

  private async getColumns(tableName: string): Promise<ColumnInfo[]> {
    // 获取基本列信息和注释
    const basicResult = await this.client.query(`
      SELECT
        c.column_name,
        c.data_type,
        c.character_maximum_length,
        c.numeric_precision,
        c.numeric_scale,
        c.is_nullable,
        c.column_default,
        c.udt_name,
        pgd.description as column_comment
      FROM information_schema.columns c
      LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
      LEFT JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace AND pgn.nspname = c.table_schema
      LEFT JOIN pg_attribute pga ON pga.attrelid = pgc.oid AND pga.attname = c.column_name
      LEFT JOIN pg_description pgd ON pgd.objoid = pgc.oid AND pgd.objsubid = pga.attnum
      WHERE c.table_schema = 'public' AND c.table_name = $1
      ORDER BY c.ordinal_position
    `, [tableName]);

    // 尝试获取 geometry 类型的详细信息
    const columnsWithGeometry = await Promise.all(
      basicResult.rows.map(async (row) => {
        let geometryTypeInfo = null;

        if (row.udt_name === 'geometry') {
          try {
            const geometryResult = await this.client.query(`
              SELECT
                'geometry(' ||
                COALESCE(type, 'GEOMETRY') ||
                CASE WHEN srid IS NOT NULL THEN ', ' || srid::text ELSE '' END ||
                ')' as full_type
              FROM geometry_columns
              WHERE f_table_schema = 'public'
              AND f_table_name = $1
              AND f_geometry_column = $2
            `, [tableName, row.column_name]);

            if (geometryResult.rows.length > 0) {
              geometryTypeInfo = geometryResult.rows[0].full_type;
            }
          } catch (error) {
            // PostGIS 不可用或 geometry_columns 表不存在，使用默认值
            console.warn(`无法获取 geometry 类型详细信息: ${error instanceof Error ? error.message : error}`);
          }
        }

        return {
          ...row,
          geometry_type_info: geometryTypeInfo
        };
      })
    );

    return columnsWithGeometry.map(row => ({
      name: row.column_name,
      type: this.formatPostgreSQLType(row),
      length: row.character_maximum_length,
      precision: row.numeric_precision,
      scale: row.numeric_scale,
      nullable: row.is_nullable === 'YES',
      defaultValue: row.column_default,
      autoIncrement: row.column_default?.includes('nextval'),
      comment: row.column_comment || undefined // 使用查询到的注释
    }));
  }

  private formatPostgreSQLType(row: any): string {
    let type = row.data_type;

    // 处理特殊类型
    if (type === 'USER-DEFINED') {
      // 如果是 geometry 类型且有详细信息，使用完整定义
      if (row.udt_name === 'geometry' && row.geometry_type_info) {
        return row.geometry_type_info;
      }
      type = row.udt_name;
    }

    if (row.character_maximum_length) {
      type += `(${row.character_maximum_length})`;
    } else if (row.numeric_precision && row.numeric_scale !== null) {
      type += `(${row.numeric_precision},${row.numeric_scale})`;
    } else if (row.numeric_precision) {
      type += `(${row.numeric_precision})`;
    }

    return type;
  }

  private async getIndexes(tableName: string): Promise<IndexInfo[]> {
    const result = await this.client.query(`
      SELECT
        i.relname as index_name,
        a.attname as column_name,
        ix.indisunique as is_unique,
        am.amname as index_type
      FROM pg_class t
      JOIN pg_index ix ON t.oid = ix.indrelid
      JOIN pg_class i ON i.oid = ix.indexrelid
      JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
      JOIN pg_am am ON i.relam = am.oid
      WHERE t.relname = $1 AND t.relkind = 'r'
      AND NOT ix.indisprimary
      ORDER BY i.relname, a.attnum
    `, [tableName]);

    const indexMap = new Map<string, IndexInfo>();

    result.rows.forEach(row => {
      if (!indexMap.has(row.index_name)) {
        indexMap.set(row.index_name, {
          name: row.index_name,
          columns: [],
          unique: row.is_unique,
          type: row.index_type.toUpperCase() as any
        });
      }

      indexMap.get(row.index_name)!.columns.push(row.column_name);
    });

    return Array.from(indexMap.values());
  }

  private async getPrimaryKey(tableName: string): Promise<string[] | undefined> {
    const result = await this.client.query(`
      SELECT a.attname as column_name
      FROM pg_constraint c
      JOIN pg_class t ON c.conrelid = t.oid
      JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(c.conkey)
      WHERE t.relname = $1 AND c.contype = 'p'
      ORDER BY array_position(c.conkey, a.attnum)
    `, [tableName]);

    const columns = result.rows.map(row => row.column_name);
    return columns.length > 0 ? columns : undefined;
  }

  async getTableData(tableName: string, limit: number = 1000): Promise<Record<string, any>[]> {
    const result = await this.client.query(
      `SELECT * FROM "${tableName}" LIMIT $1`,
      [limit]
    );
    return result.rows;
  }

  async getRowCount(tableName: string): Promise<number> {
    const result = await this.client.query(
      `SELECT COUNT(*) as count FROM "${tableName}"`
    );
    return parseInt(result.rows[0].count);
  }
}

/**
 * 从配置中提取数据库名
 */
function extractDatabaseName(config: DatabaseConfig): string {
  if (config.db) {
    return config.db;
  }

  if (config.source) {
    // 从连接字符串中提取数据库名
    if (config.source.includes('@tcp(')) {
      // 类似 MySQL 格式: user:password@tcp(host:port)/database
      const match = config.source.match(/@tcp\([^)]+\)\/([^?]+)/);
      if (match) {
        return match[1];
      }
    } else if (config.source.includes('://')) {
      // 标准 URL 格式: protocol://user:password@host:port/database
      try {
        const url = new URL(config.source);
        return url.pathname.slice(1); // 移除开头的 '/'
      } catch {
        // 忽略 URL 解析错误
      }
    }
  }

  throw new Error('无法从配置中提取数据库名');
}

/**
 * 创建数据库元数据查询实例
 */
export async function createMetadata(config: DatabaseConfig): Promise<DatabaseMetadata> {
  const driver = getDriver(config.driver);
  const connection = await driver.connect(config);
  const dbName = extractDatabaseName(config);

  switch (config.driver) {
    case 'mysql':
      return new MySQLMetadata(connection as mysql.Connection, dbName, config);
    case 'postgresql':
    case 'postgres':
      return new PostgreSQLMetadata(connection as pg.Client, dbName, config);
    default:
      throw new Error(`不支持的数据库驱动: ${config.driver}`);
  }
}
