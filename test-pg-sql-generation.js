#!/usr/bin/env node

// 测试 PostgreSQL SQL 生成
import { PostgreSQLGenerator } from './dist/migration/generator.js';

console.log('🧪 测试 PostgreSQL SQL 生成器...\n');

const generator = new PostgreSQLGenerator();

// 测试表信息 - 模拟 work_wx_approval_messages 表结构
const testTable = {
  name: 'work_wx_approval_messages',
  columns: [
    {
      name: 'id',
      type: 'character varying',
      nullable: false
    },
    {
      name: 'created_at',
      type: 'timestamp with time zone',
      nullable: false
    },
    {
      name: 'updated_at',
      type: 'timestamp with time zone',
      nullable: false
    },
    {
      name: 'created_by',
      type: 'character varying',
      nullable: true
    },
    {
      name: 'status',
      type: 'integer',
      nullable: false,
      defaultValue: '0'
    },
    {
      name: 'create_time',
      type: 'bigint(64,0)',
      nullable: false
    },
    {
      name: 'agent_id',
      type: 'bigint(64,0)',
      nullable: false
    },
    {
      name: 'open_sp_status',
      type: 'bigint(64,0)',
      nullable: false
    },
    {
      name: 'apply_time',
      type: 'bigint(64,0)',
      nullable: false
    },
    {
      name: 'approver_step',
      type: 'bigint(64,0)',
      nullable: false
    }
  ],
  indexes: [],
  primaryKey: []
};

console.log('📋 测试 CREATE TABLE 语句:');
const createTableOp = generator.generateCreateTable(testTable);
console.log(createTableOp.sql);
console.log('');

console.log('📋 测试 ADD COLUMN 语句:');
const newColumn = {
  name: 'email',
  type: 'CHARACTER VARYING(255)',
  nullable: true,
  defaultValue: null
};
const addColumnOp = generator.generateAddColumn('test_table', newColumn);
console.log(addColumnOp.sql);
console.log('');

console.log('📋 测试 MODIFY COLUMN 语句:');
const oldColumn = {
  name: 'age',
  type: 'INTEGER(32,0)',
  nullable: true
};
const modifiedColumn = {
  name: 'age',
  type: 'BIGINT(64,0)',
  nullable: false,
  defaultValue: '0'
};
const modifyColumnOp = generator.generateModifyColumn('test_table', oldColumn, modifiedColumn);
console.log(modifyColumnOp.sql);
console.log('');

console.log('📋 测试 ADD INDEX 语句:');
const newIndex = {
  name: 'idx_email',
  columns: ['email'],
  unique: false,
  type: 'BTREE'
};
const addIndexOp = generator.generateAddIndex('test_table', newIndex);
console.log(addIndexOp.sql);
console.log('');

console.log('📋 测试 INSERT DATA 语句:');
const testData = [
  { id: 1, name: 'John', age: 30, is_active: true },
  { id: 2, name: 'Jane', age: 25, is_active: false }
];
const insertDataOp = generator.generateInsertData('test_table', testData);
console.log(insertDataOp.sql);
console.log('');

console.log('✅ PostgreSQL SQL 生成测试完成！');
console.log('');
console.log('🔍 检查要点:');
console.log('1. 数据类型是否使用 PostgreSQL 原生类型（int4, varchar, etc.）');
console.log('2. 是否包含模式名 "public"');
console.log('3. varchar 类型是否包含 COLLATE 子句');
console.log('4. CREATE TABLE 格式是否符合 PostgreSQL 原生风格');
console.log('5. 主键约束是否作为单独的 ALTER TABLE 语句');

console.log('\n📋 期望的格式类似:');
console.log('CREATE TABLE "public"."work_wx_approval_messages" (');
console.log('  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,');
console.log('  "created_at" timestamptz(6) NOT NULL,');
console.log('  "status" int4 NOT NULL DEFAULT 0,');
console.log('  "create_time" int8 NOT NULL,');
console.log('  "agent_id" int8 NOT NULL,');
console.log('  "apply_time" int8 NOT NULL');
console.log(');');
