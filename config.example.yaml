# 数据库配置示例文件
# 复制此文件为 config.yaml 并修改相应的配置

# MySQL 数据库配置示例 1 - 使用详细配置
db1:
  driver: mysql
  host: localhost
  port: 3306
  user: root
  password: your_password
  db: your_database
  charset: utf8mb4
  # 表过滤配置（可选）
  excludeTables:
    - temp_table
    - log_table
    - cache_table
  # includeTables:
  #   - users
  #   - products
  #   - orders

# MySQL 数据库配置示例 2 - 使用连接字符串
db2:
  driver: mysql
  source: 'root:password@tcp(localhost:3306)/database?charset=utf8mb4&parseTime=True&loc=Local'
  # 表过滤配置也可以和连接字符串一起使用
  excludeTables:
    - sessions
    - migrations

# 开发环境数据库
dev_db:
  driver: mysql
  host: localhost
  port: 3306
  user: dev_user
  password: dev_password
  db: development
  charset: utf8mb4
  # 开发环境通常排除日志和临时表
  excludeTables:
    - access_logs
    - error_logs
    - debug_info
    - temp_*  # 支持通配符模式（待实现）

# 测试环境数据库
test_db:
  driver: mysql
  host: test.example.com
  port: 3306
  user: test_user
  password: test_password
  db: testing
  charset: utf8mb4
  # 测试环境只包含核心表
  includeTables:
    - users
    - products
    - orders
    - categories

# PostgreSQL 数据库配置示例 1 - 使用详细配置
pg_db1:
  driver: postgresql
  host: localhost
  port: 5432
  user: postgres
  password: your_password
  db: your_database
  excludeTables:
    - spatial_ref_sys  # PostGIS 系统表
    - geometry_columns

# PostgreSQL 数据库配置示例 2 - 使用连接字符串 (类似 MySQL 格式)
pg_db2:
  driver: postgresql
  source: 'postgres:password@tcp(localhost:5432)/database'

# 生产环境数据库 (谨慎使用)
# prod_db:
#   driver: mysql
#   host: prod.example.com
#   port: 3306
#   user: readonly_user
#   password: readonly_password
#   db: production
#   charset: utf8mb4
#   # 生产环境排除敏感和临时数据
#   excludeTables:
#     - user_sessions
#     - audit_logs
#     - system_logs
#     - temp_uploads
