#!/usr/bin/env node

// 配置兼容性测试脚本
import { loadConfig, validateDatabaseConfig, getConnectionString } from './dist/utils/config.js';

console.log('🧪 测试配置文件兼容性...\n');

try {
  // 测试加载原 Go 版本的配置文件
  console.log('📁 加载配置文件: config.yaml');
  const config = loadConfig('config.yaml');
  
  console.log('✅ 配置文件加载成功');
  console.log(`📊 找到 ${Object.keys(config).length} 个数据库配置:\n`);
  
  // 遍历每个数据库配置
  for (const [name, dbConfig] of Object.entries(config)) {
    console.log(`🔍 测试数据库配置: ${name}`);
    console.log(`   驱动: ${dbConfig.driver}`);
    
    try {
      // 验证配置
      validateDatabaseConfig(dbConfig);
      console.log('   ✅ 配置验证通过');
      
      // 生成连接字符串
      if (dbConfig.source) {
        console.log(`   🔗 使用直接连接字符串: ${dbConfig.source.substring(0, 50)}...`);
      } else {
        const connectionString = getConnectionString(dbConfig);
        console.log(`   🔗 生成连接字符串: ${connectionString.substring(0, 50)}...`);
      }
      
    } catch (error) {
      console.log(`   ❌ 配置验证失败: ${error.message}`);
    }
    
    console.log('');
  }
  
  console.log('🎉 配置兼容性测试完成！');
  console.log('✅ TypeScript 版本与 Go 版本配置完全兼容');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  process.exit(1);
}
