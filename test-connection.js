#!/usr/bin/env node

// 连接字符串解析测试脚本
import { MySQLDriver } from './dist/database/driver.js';

console.log('🧪 测试 MySQL 连接字符串解析...\n');

const driver = new MySQLDriver();

// 测试用例
const testCases = [
  {
    name: 'Go 格式连接字符串 (带 tcp)',
    source: 'root:password@tcp(localhost:3306)/testdb?charset=utf8mb4&parseTime=True&loc=Local'
  },
  {
    name: 'Go 格式连接字符串 (复杂密码)',
    source: 'root:E[yA-mDdgUe@tcp(*************:3308)/sj-finance?charset=utf8mb4&parseTime=True&loc=Local'
  },
  {
    name: '详细配置',
    config: {
      driver: 'mysql',
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'password',
      db: 'testdb',
      charset: 'utf8mb4'
    }
  }
];

for (const testCase of testCases) {
  console.log(`🔍 测试: ${testCase.name}`);
  
  try {
    let connectionOptions;
    
    if (testCase.source) {
      // 测试连接字符串解析
      console.log(`   输入: ${testCase.source}`);
      connectionOptions = driver.parseGoConnectionString(testCase.source);
    } else if (testCase.config) {
      // 测试详细配置
      console.log(`   配置: ${JSON.stringify(testCase.config, null, 2)}`);
      connectionOptions = {
        host: testCase.config.host || 'localhost',
        port: testCase.config.port || 3306,
        user: testCase.config.user,
        password: testCase.config.password,
        database: testCase.config.db,
        charset: testCase.config.charset || 'utf8mb4',
        timezone: '+00:00',
        dateStrings: false
      };
    }
    
    console.log('   ✅ 解析成功:');
    console.log(`      主机: ${connectionOptions.host}:${connectionOptions.port}`);
    console.log(`      用户: ${connectionOptions.user}`);
    console.log(`      密码: ${'*'.repeat(connectionOptions.password?.length || 0)}`);
    console.log(`      数据库: ${connectionOptions.database}`);
    console.log(`      字符集: ${connectionOptions.charset}`);
    
  } catch (error) {
    console.log(`   ❌ 解析失败: ${error.message}`);
  }
  
  console.log('');
}

console.log('🎉 连接字符串解析测试完成！');
console.log('💡 注意: 这只是解析测试，不会实际连接数据库');
