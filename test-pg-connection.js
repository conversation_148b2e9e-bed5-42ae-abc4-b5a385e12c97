#!/usr/bin/env node

// 测试 PostgreSQL 连接
import pg from 'pg';

console.log('🔍 测试 PostgreSQL 连接...\n');

const connectionOptions = {
  host: '***************',
  port: 5432,
  user: 'postgres',
  password: '123456',
  database: 'demo'
};

console.log('连接选项:');
console.log(JSON.stringify(connectionOptions, null, 2));

async function testConnection() {
  const client = new pg.Client(connectionOptions);
  
  try {
    console.log('\n🔌 尝试连接...');
    await client.connect();
    console.log('✅ 连接成功！');
    
    console.log('\n📊 测试查询...');
    const result = await client.query('SELECT version()');
    console.log('✅ 查询成功！');
    console.log('PostgreSQL 版本:', result.rows[0].version);
    
  } catch (error) {
    console.log('❌ 连接失败:', error.message);
    console.log('错误详情:', error);
  } finally {
    try {
      await client.end();
      console.log('🔒 连接已关闭');
    } catch (e) {
      // 忽略关闭错误
    }
  }
}

testConnection();
