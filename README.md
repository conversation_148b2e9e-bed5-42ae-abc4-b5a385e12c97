# DBTool - 数据库命令行工具

一个基于 React + Ink 的现代化数据库命令行工具，提供美观的终端界面和强大的数据浏览功能。

## 功能特性

### 🔍 查询功能
- 🔗 数据库连接测试
- 📊 现代化分页表格展示和导航
- ✨ 基于 React + Ink 的美观界面
- 🎯 智能焦点切换和键盘快捷键
- 📄 分页浏览（每页10行）

### 🔄 迁移功能
- 📋 自动比较数据库结构差异
- 🏗️ 生成 CREATE、ALTER、INSERT、UPDATE SQL
- 📁 按时间戳命名的迁移文件
- 🔧 支持跨数据库类型迁移
- 📝 详细的 SQL 注释和操作说明
- 🎯 智能表过滤功能（支持包含/排除特定表）
- ⚙️ 配置文件和命令行双重过滤支持

### 🗄️ 数据库支持
- ✅ MySQL（完整支持）
- ✅ PostgreSQL（完整支持）
- 🚀 快速响应和易于使用
- ⚙️ 灵活的配置文件格式

## 安装

### 从源码安装

```bash
git clone <repository>
cd dbtool
npm install
npm run build
```

### 全局安装

```bash
npm install -g .
```

## 配置

创建 `config.yaml` 配置文件：

```yaml
# MySQL 配置示例
db1:
  driver: mysql
  source: 'root:123456@tcp(127.0.0.1:3306)/threebody-admin?charset=utf8mb4&parseTime=True&loc=Local'

db2:
  driver: mysql
  host: 127.0.0.1
  port: 3306
  db: threebody-admin
  user: root
  password: 123456
  charset: utf8mb4

# PostgreSQL 配置示例
pg_db1:
  driver: postgresql
  source: 'postgres:password@tcp(localhost:5432)/database'

pg_db2:
  driver: postgresql
  host: localhost
  port: 5432
  db: database
  user: postgres
  password: password
```

## 使用方法

### 查询功能（默认）

```bash
# 启动交互式查询界面
npm run dev

# 或者明确指定 query 子命令
npm run dev query

# 使用编译后的版本
npm run build
node dist/index.js

# 指定配置文件
node dist/index.js -c config.yaml
```

### 迁移功能

```bash
# 基本迁移（仅结构）
npm run dev migrate source_db target_db ./migrations

# 包含数据迁移
npm run dev migrate --include-data source_db target_db ./migrations

# 排除特定表
npm run dev migrate --exclude logs,sessions,temp_data source_db target_db ./migrations

# 仅包含特定表
npm run dev migrate --include users,products,orders source_db target_db ./migrations

# 查看迁移帮助
npm run dev migrate --help

# 使用编译后的版本
node dist/index.js migrate source_db target_db ./migrations
```

## 表过滤功能

### 配置文件中的表过滤

```yaml
# 排除特定表
source_db:
  driver: postgresql
  host: localhost
  port: 5432
  db: myapp
  user: postgres
  password: password
  excludeTables:
    - logs
    - sessions
    - cache_data
    - temp_uploads

# 仅包含特定表
target_db:
  driver: mysql
  host: localhost
  port: 3306
  db: myapp_mysql
  user: root
  password: password
  includeTables:
    - users
    - products
    - orders
    - categories

# 同时使用包含和排除
filtered_db:
  driver: postgresql
  host: localhost
  port: 5432
  db: myapp
  user: postgres
  password: password
  includeTables:
    - users
    - products
    - orders
    - admin_logs
  excludeTables:
    - admin_logs  # 从包含的表中排除
```

### 命令行表过滤

```bash
# 排除表（覆盖配置文件设置）
dbtool migrate --exclude logs,sessions,cache_data source_db target_db ./migrations

# 包含表（覆盖配置文件设置）
dbtool migrate --include users,products,orders source_db target_db ./migrations

# 同时使用包含和排除（先包含后排除）
dbtool migrate --include users,products,orders,logs --exclude logs source_db target_db ./migrations
```

### 过滤规则

1. **命令行参数** 优先级最高，会覆盖配置文件设置
2. **includeTables** 优先于 excludeTables
3. **excludeTables** 在 includeTables 之后应用
4. 表名匹配区分大小写，使用精确匹配
5. 不存在的表名会被忽略，不会导致错误

### 现代化界面交互

**操作步骤：**
1. 启动后显示数据库选择界面（如果有多个数据库）
2. 使用 `↑/↓` 选择数据库，`Enter` 确认
3. 进入查询输入界面，输入SQL语句
4. 按 `Enter` 执行查询，结果显示在下方表格中
5. 使用 `Tab` 键在不同组件间切换焦点

**表格导航：**
- `↑↓` 或 `j/k`: 行导航（自动跨页）
- `←→` 或 `h/l`: 列导航（循环滚动）
- `PgUp/PgDn`: 快速翻页
- `v`: 查看单元格详情
- `Tab`: 切换焦点

### 测试功能

```bash
# 测试配置文件
npm run test-config

# 测试数据库连接
npm run test-connection

# 测试现代化表格功能
npm run test-modern-table
```

## 配置格式说明

支持两种配置格式：

### 1. 连接字符串格式（推荐）

```yaml
db1:
  driver: mysql
  source: 'root:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local'
```

### 2. 分段配置格式

```yaml
db2:
  driver: mysql
  host: 127.0.0.1
  port: 3306
  db: database_name
  user: username
  password: password
  charset: utf8mb4
```

## 支持的数据库

- ✅ MySQL
- ✅ PostgreSQL
- 🔄 SQLite (计划中)

## 开发

### 项目结构

```
dbtool/
├── cmd/              # CLI命令定义
├── internal/
│   ├── config/       # 配置文件处理
│   ├── database/     # 数据库驱动
│   └── tui/          # TUI界面
├── main.go
├── config.yaml
└── go.mod
```

### 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
