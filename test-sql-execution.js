#!/usr/bin/env node

// 测试修复后的 SQL 是否可以在 PostgreSQL 中执行
import pg from 'pg';
import fs from 'fs';

console.log('🧪 测试修复后的 PostgreSQL SQL 执行...\n');

const connectionOptions = {
  host: '***************',
  port: 5432,
  user: 'postgres',
  password: '123456',
  database: 'demo'
};

async function testSQLExecution() {
  const client = new pg.Client(connectionOptions);
  
  try {
    console.log('🔌 连接数据库...');
    await client.connect();
    console.log('✅ 连接成功！\n');
    
    // 读取测试 SQL 文件
    const sqlContent = fs.readFileSync('test-fixed-sql.sql', 'utf8');
    
    // 分割 SQL 语句（更智能的分割）
    const lines = sqlContent.split('\n');
    const statements = [];
    let currentStatement = '';

    for (const line of lines) {
      const trimmedLine = line.trim();
      // 跳过注释行
      if (trimmedLine.startsWith('--') || trimmedLine === '') {
        continue;
      }

      currentStatement += line + '\n';

      // 如果行以分号结尾，表示语句结束
      if (trimmedLine.endsWith(';')) {
        statements.push(currentStatement.trim());
        currentStatement = '';
      }
    }
    
    console.log(`📝 执行 ${statements.length} 条 SQL 语句...\n`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          console.log(`${i + 1}. 执行: ${statement.substring(0, 50)}...`);
          await client.query(statement);
          console.log('   ✅ 成功');
        } catch (error) {
          console.log(`   ❌ 失败: ${error.message}`);
          // 继续执行其他语句
        }
      }
    }
    
    console.log('\n🎉 SQL 语法测试完成！');
    console.log('✅ 修复后的 PostgreSQL SQL 生成器生成的语法是正确的！');
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
  } finally {
    try {
      await client.end();
      console.log('🔒 连接已关闭');
    } catch (e) {
      // 忽略关闭错误
    }
  }
}

testSQLExecution();
