# 测试配置文件 - 仅用于演示，不包含真实数据库连接信息
db1:
  driver: mysql
  host: localhost
  port: 3306
  user: test_user
  password: test_password
  db: test_database
  charset: utf8mb4

# 带表过滤功能的测试配置
# 此配置演示如何在数据库配置中使用表过滤功能

# 源数据库 - 包含表过滤配置
source_with_filter:
  driver: postgresql
  host: 127.0.0.1
  port: 5432
  db: wms
  user: postgres
  password: postgres
  charset: utf8mb4
  # 排除系统表和日志表
  excludeTables:
    - spatial_ref_sys
    - geometry_columns
    - flyway_schema_history
    - pg_stat_statements
    - pg_buffercache

# 目标数据库 - 只包含核心业务表
target_core_only:
  driver: postgresql
  host: 127.0.0.1
  port: 5432
  db: wms_target
  user: postgres
  password: postgres
  charset: utf8mb4
  # 只包含核心业务表
  includeTables:
    - users
    - products
    - orders
    - categories
    - wms_gps_records

# MySQL 示例 - 排除临时表和缓存表
mysql_prod:
  driver: mysql
  host: localhost
  port: 3306
  user: readonly_user
  password: readonly_password
  db: production
  charset: utf8mb4
  excludeTables:
    - temp_uploads
    - cache_data
    - session_data
    - error_logs
    - access_logs
    - migration_backup_*  # 支持通配符（未来版本）

# 开发环境 - 包含所有表（不过滤）
dev_full:
  driver: postgresql
  host: 127.0.0.1
  port: 5432
  db: wms
  user: postgres
  password: postgres
  # 不设置任何过滤，包含所有表

# 测试用配置 - 只有特定的测试表
test_specific:
  driver: postgresql
  host: 127.0.0.1
  port: 5432
  db: wms
  user: postgres
  password: postgres
  includeTables:
    - test_table_1
    - test_table_2
    - wms_gps_records
  # 即使在包含的表中，也可以再排除某些表
  excludeTables:
    - test_table_2  # 这将从 includeTables 中排除

# PostgreSQL 原配置（用于对比测试）
db2:
  driver: postgresql
  host: 127.0.0.1
  port: 5432
  db: wms
  user: postgres
  password: postgres
  charset: utf8mb4

# PostgreSQL 测试配置
pg_db1:
  driver: postgresql
  host: localhost
  port: 5432
  user: test_user
  password: test_password
  db: test_database

pg_db2:
  driver: postgresql
  source: 'demo:demo@tcp(localhost:5432)/demo_db'
