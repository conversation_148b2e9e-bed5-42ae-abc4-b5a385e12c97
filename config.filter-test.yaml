# 表过滤测试配置
source_filtered:
  driver: postgresql
  host: 127.0.0.1
  port: 5432
  db: wms
  user: postgres
  password: postgres
  # 排除一些表以测试过滤功能
  excludeTables:
    - sys_logs
    - sys_user_logs
    - wms_operate_logs
    - wms_access_door_logs
    - wms_material_logs

target_minimal:
  driver: postgresql
  host: 127.0.0.1
  port: 5432
  db: wms
  user: postgres
  password: postgres
  # 只包含少量核心表
  includeTables:
    - sys_users
    - sys_roles
    - wms_companies
    - wms_repositories