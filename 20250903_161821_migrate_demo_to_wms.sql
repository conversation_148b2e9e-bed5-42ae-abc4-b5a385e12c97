-- =====================================================
-- 数据库迁移文件
-- =====================================================
-- 生成时间: 2025-09-03 08:18:21
-- 时间戳: 20250903_161821
-- 源数据库: demo
-- 目标数据库: wms
-- 生成工具: dbtool migrate
-- =====================================================
-- 
-- 注意事项:
-- 1. 请在执行前备份目标数据库
-- 2. 建议在测试环境中先验证迁移脚本
-- 3. 执行前请检查所有 SQL 语句的正确性
-- 4. 如有数据迁移，请注意数据一致性
-- 
-- =====================================================

-- 创建表 spatial_ref_sys
-- 操作类型: CREATE_TABLE
-- 表名: spatial_ref_sys
CREATE TABLE "public"."spatial_ref_sys" (
  "srid" int4 NOT NULL,
  "auth_name" varchar(256) COLLATE "pg_catalog"."default",
  "auth_srid" int4,
  "srtext" varchar(2048) COLLATE "pg_catalog"."default",
  "proj4text" varchar(2048) COLLATE "pg_catalog"."default",
  PRIMARY KEY ("srid")
);

-- 创建表 wms_warning_rules
-- 操作类型: CREATE_TABLE
-- 表名: wms_warning_rules
CREATE TABLE "public"."wms_warning_rules" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "repository_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_type_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "operator" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" int4 NOT NULL,
  "is_active" bool NOT NULL DEFAULT true,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_warning_rules"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_warning_rules"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_warning_rules"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_warning_rules"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_warning_rules"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_warning_rules"."repository_id" IS '仓库ID';
COMMENT ON COLUMN "public"."wms_warning_rules"."equipment_type_id" IS '装备类型';
COMMENT ON COLUMN "public"."wms_warning_rules"."operator" IS '操作符';
COMMENT ON COLUMN "public"."wms_warning_rules"."value" IS '阈值';
COMMENT ON COLUMN "public"."wms_warning_rules"."is_active" IS '是否启用';

-- 创建表 wms_gps_records
-- 操作类型: CREATE_TABLE
-- 表名: wms_gps_records
CREATE TABLE "public"."wms_gps_records" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "code" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "location" geometry(POINT, 4326),
  "longitude" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "latitude" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "altitude" varchar COLLATE "pg_catalog"."default",
  "speed" varchar COLLATE "pg_catalog"."default",
  "direction" varchar COLLATE "pg_catalog"."default",
  "gps_time" timestamptz(6) NOT NULL,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_gps_records"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_gps_records"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_gps_records"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_gps_records"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_gps_records"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_gps_records"."code" IS '设备编码';
COMMENT ON COLUMN "public"."wms_gps_records"."location" IS '空间点位';
COMMENT ON COLUMN "public"."wms_gps_records"."longitude" IS '经度';
COMMENT ON COLUMN "public"."wms_gps_records"."latitude" IS '纬度';
COMMENT ON COLUMN "public"."wms_gps_records"."altitude" IS '海拔';
COMMENT ON COLUMN "public"."wms_gps_records"."speed" IS '速度';
COMMENT ON COLUMN "public"."wms_gps_records"."direction" IS '方向';
COMMENT ON COLUMN "public"."wms_gps_records"."gps_time" IS 'GPS时间';

-- 创建表 work_wx_approval_messages
-- 操作类型: CREATE_TABLE
-- 表名: work_wx_approval_messages
CREATE TABLE "public"."work_wx_approval_messages" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL DEFAULT '0',
  "to_user_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "from_user_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" int8 NOT NULL,
  "msg_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "event" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "agent_id" int8 NOT NULL,
  "third_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "sp_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "open_sp_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "open_template_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "open_sp_status" int8 NOT NULL,
  "apply_time" int8 NOT NULL,
  "apply_user_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "apply_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "apply_user_party" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "apply_user_image" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "approver_step" int8 NOT NULL,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."work_wx_approval_messages"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."remark" IS '备注';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."status" IS '状态：0 禁用、 1启用';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."to_user_name" IS '接收消息的企业微信CorpID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."from_user_name" IS '发送消息的企业微信账号ID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."create_time" IS '消息创建时间（Unix时间戳）';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."msg_type" IS '消息类型';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."event" IS '事件类型';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."agent_id" IS '企业应用ID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."third_no" IS '审批单编号';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."sp_type" IS '审批类型';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."open_sp_name" IS '审批模板名称';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."open_template_id" IS '审批模板ID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."open_sp_status" IS '审批状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_time" IS '提交审批时间（Unix时间戳）';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_name" IS '提交审批人姓名';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_id" IS '提交审批人UserID';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_party" IS '提交审批人所在部门';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."apply_user_image" IS '提交审批人头像URL';
COMMENT ON COLUMN "public"."work_wx_approval_messages"."approver_step" IS '审批流程当前审批节点次序（从0开始）';

-- 创建表 work_wx_approval_nodes
-- 操作类型: CREATE_TABLE
-- 表名: work_wx_approval_nodes
CREATE TABLE "public"."work_wx_approval_nodes" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "node_status" int8 NOT NULL,
  "node_attr" int8 NOT NULL,
  "node_type" int8 NOT NULL,
  "item_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_image" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_status" int8 NOT NULL,
  "item_speech" varchar COLLATE "pg_catalog"."default",
  "item_op_time" int8 NOT NULL,
  "approval_message_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."work_wx_approval_nodes"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."node_status" IS '节点状态：1-审批中；2-已同意；3-已驳回；4-已转审';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."node_attr" IS '节点类型：1-或签；2-会签；3-单签';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."node_type" IS '审批节点类型：1-固定成员；2-标签；3-上级；4-上级的上级...以此类推';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_name" IS '审批人姓名';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_image" IS '审批人头像URL';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_user_id" IS '审批人UserID';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_status" IS '审批状态：1-审批中；2-已同意；3-已驳回；4-已转审';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_speech" IS '审批意见';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."item_op_time" IS '操作时间（Unix时间戳）';
COMMENT ON COLUMN "public"."work_wx_approval_nodes"."approval_message_id" IS '关联的审批消息ID';

-- 创建表 work_wx_notify_nodes
-- 操作类型: CREATE_TABLE
-- 表名: work_wx_notify_nodes
CREATE TABLE "public"."work_wx_notify_nodes" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "item_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_image" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "item_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "approval_message_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."work_wx_notify_nodes"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."item_name" IS '抄送人姓名';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."item_image" IS '抄送人头像URL';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."item_user_id" IS '抄送人UserID';
COMMENT ON COLUMN "public"."work_wx_notify_nodes"."approval_message_id" IS '关联的审批消息ID';

-- 创建表 wms_borrow_order_details
-- 操作类型: CREATE_TABLE
-- 表名: wms_borrow_order_details
CREATE TABLE "public"."wms_borrow_order_details" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "order_id" varchar COLLATE "pg_catalog"."default",
  "equipment_type_id" varchar COLLATE "pg_catalog"."default",
  "equipment_id" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default",
  "model_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "measure_unit_id" varchar COLLATE "pg_catalog"."default",
  "num" int8 NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "return_time" timestamptz(6),
  "is_return" bool NOT NULL DEFAULT false,
  "feature" jsonb,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_borrow_order_details"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."order_id" IS '借用单';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."equipment_type_id" IS '设备类型';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."equipment_id" IS '设备';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."name" IS '设备名称';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."model_no" IS '规格型号';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."measure_unit_id" IS '计量单位';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."num" IS '数量';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."return_time" IS '归还时间';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."is_return" IS '是否归还';
COMMENT ON COLUMN "public"."wms_borrow_order_details"."feature" IS '详细规格';

-- 创建表 wms_borrow_orders
-- 操作类型: CREATE_TABLE
-- 表名: wms_borrow_orders
CREATE TABLE "public"."wms_borrow_orders" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "contract_urls" varchar COLLATE "pg_catalog"."default",
  "invoice_urls" varchar COLLATE "pg_catalog"."default",
  "audit_urls" varchar COLLATE "pg_catalog"."default",
  "other_urls" varchar COLLATE "pg_catalog"."default",
  "order_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "borrow_time" timestamptz(6) NOT NULL,
  "expect_return_time" timestamptz(6) NOT NULL,
  "borrower_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_num" int8,
  "reason" text COLLATE "pg_catalog"."default",
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_borrow_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_borrow_orders"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_borrow_orders"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_borrow_orders"."contract_urls" IS '合同附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."invoice_urls" IS '发票附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."audit_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."other_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_borrow_orders"."order_no" IS '借用单号';
COMMENT ON COLUMN "public"."wms_borrow_orders"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_borrow_orders"."borrow_time" IS '借用时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."expect_return_time" IS '预期归还时间';
COMMENT ON COLUMN "public"."wms_borrow_orders"."borrower_id" IS '借用人';
COMMENT ON COLUMN "public"."wms_borrow_orders"."status" IS '借用状态';
COMMENT ON COLUMN "public"."wms_borrow_orders"."equipment_num" IS '设备数量';
COMMENT ON COLUMN "public"."wms_borrow_orders"."reason" IS '事由';

-- 创建表 wms_claim_orders
-- 操作类型: CREATE_TABLE
-- 表名: wms_claim_orders
CREATE TABLE "public"."wms_claim_orders" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "contract_urls" varchar COLLATE "pg_catalog"."default",
  "invoice_urls" varchar COLLATE "pg_catalog"."default",
  "audit_urls" varchar COLLATE "pg_catalog"."default",
  "other_urls" varchar COLLATE "pg_catalog"."default",
  "order_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "claim_time" timestamptz(6) NOT NULL,
  "claim_user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_num" int8,
  "reason" text COLLATE "pg_catalog"."default",
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_claim_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_claim_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_claim_orders"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_claim_orders"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_claim_orders"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_claim_orders"."contract_urls" IS '合同附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."invoice_urls" IS '发票附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."audit_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."other_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_claim_orders"."order_no" IS '领用单号';
COMMENT ON COLUMN "public"."wms_claim_orders"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_claim_orders"."claim_time" IS '领用时间';
COMMENT ON COLUMN "public"."wms_claim_orders"."claim_user_id" IS '领用人';
COMMENT ON COLUMN "public"."wms_claim_orders"."status" IS '领用状态';
COMMENT ON COLUMN "public"."wms_claim_orders"."equipment_num" IS '设备数量';
COMMENT ON COLUMN "public"."wms_claim_orders"."reason" IS '事由';

-- 创建表 wms_claim_order_details
-- 操作类型: CREATE_TABLE
-- 表名: wms_claim_order_details
CREATE TABLE "public"."wms_claim_order_details" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "order_id" varchar COLLATE "pg_catalog"."default",
  "equipment_type_id" varchar COLLATE "pg_catalog"."default",
  "equipment_id" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default",
  "model_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "measure_unit_id" varchar COLLATE "pg_catalog"."default",
  "num" int8 NOT NULL,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "feature" jsonb,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_claim_order_details"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_claim_order_details"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_claim_order_details"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_claim_order_details"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_claim_order_details"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_claim_order_details"."order_id" IS '领用单';
COMMENT ON COLUMN "public"."wms_claim_order_details"."equipment_type_id" IS '设备类型';
COMMENT ON COLUMN "public"."wms_claim_order_details"."equipment_id" IS '设备';
COMMENT ON COLUMN "public"."wms_claim_order_details"."name" IS '设备名称';
COMMENT ON COLUMN "public"."wms_claim_order_details"."model_no" IS '规格型号';
COMMENT ON COLUMN "public"."wms_claim_order_details"."measure_unit_id" IS '计量单位';
COMMENT ON COLUMN "public"."wms_claim_order_details"."num" IS '数量';
COMMENT ON COLUMN "public"."wms_claim_order_details"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_claim_order_details"."feature" IS '详细规格';

-- 创建表 wms_transfer_orders
-- 操作类型: CREATE_TABLE
-- 表名: wms_transfer_orders
CREATE TABLE "public"."wms_transfer_orders" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "contract_urls" varchar COLLATE "pg_catalog"."default",
  "invoice_urls" varchar COLLATE "pg_catalog"."default",
  "audit_urls" varchar COLLATE "pg_catalog"."default",
  "other_urls" varchar COLLATE "pg_catalog"."default",
  "order_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_num" int8,
  "from_repository_id" varchar COLLATE "pg_catalog"."default",
  "to_repository_id" varchar COLLATE "pg_catalog"."default",
  "status" varchar COLLATE "pg_catalog"."default",
  "transfer_time" timestamptz(6),
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_transfer_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_transfer_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_transfer_orders"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_transfer_orders"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_transfer_orders"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_transfer_orders"."contract_urls" IS '合同附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."invoice_urls" IS '发票附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."audit_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."other_urls" IS '会审凭证附件';
COMMENT ON COLUMN "public"."wms_transfer_orders"."order_no" IS '调拨单号';
COMMENT ON COLUMN "public"."wms_transfer_orders"."equipment_num" IS '装备数量';
COMMENT ON COLUMN "public"."wms_transfer_orders"."from_repository_id" IS '调出仓库';
COMMENT ON COLUMN "public"."wms_transfer_orders"."to_repository_id" IS '调入仓库';
COMMENT ON COLUMN "public"."wms_transfer_orders"."status" IS '调拨状态';
COMMENT ON COLUMN "public"."wms_transfer_orders"."transfer_time" IS '调拨时间';

-- 创建表 wms_transfer_order_details
-- 操作类型: CREATE_TABLE
-- 表名: wms_transfer_order_details
CREATE TABLE "public"."wms_transfer_order_details" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "order_id" varchar COLLATE "pg_catalog"."default",
  "material_id" varchar COLLATE "pg_catalog"."default",
  "material_name" varchar COLLATE "pg_catalog"."default",
  "code" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_id" varchar COLLATE "pg_catalog"."default",
  "equipment_type_id" varchar COLLATE "pg_catalog"."default",
  "feature" jsonb,
  "repository_id" varchar COLLATE "pg_catalog"."default",
  "repository_area_id" varchar COLLATE "pg_catalog"."default",
  "repository_position_id" varchar COLLATE "pg_catalog"."default",
  "owner_id" varchar COLLATE "pg_catalog"."default",
  "model_no" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "measure_unit_id" varchar COLLATE "pg_catalog"."default",
  "num" int8 NOT NULL,
  "transfer_reason" text COLLATE "pg_catalog"."default",
  "transfer_time" timestamptz(6),
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "public"."wms_transfer_order_details"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."remark" IS '备注';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."order_id" IS '调拨单';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."material_id" IS '物料';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."material_name" IS '物料名称';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."code" IS '编码';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."equipment_id" IS '装备';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."equipment_type_id" IS '装备类型';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."feature" IS '详细规格';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."repository_id" IS '仓库';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."repository_area_id" IS '库区';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."repository_position_id" IS '库位';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."owner_id" IS '归属人';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."model_no" IS '规格型号';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."measure_unit_id" IS '计量单位';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."num" IS '调拨数量';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."transfer_reason" IS '调拨原因';
COMMENT ON COLUMN "public"."wms_transfer_order_details"."transfer_time" IS '调拨时间';

-- 修改表 wms_audit_plan_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_audit_plan_details
-- 字段名: num
ALTER TABLE "public"."wms_audit_plan_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_meeting_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_meeting_details
-- 字段名: num
ALTER TABLE "public"."wms_discard_meeting_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_meetings 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_meetings
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_discard_meetings" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_discard_meetings 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_meetings
-- 字段名: equipment_num
ALTER TABLE "public"."wms_discard_meetings" ALTER COLUMN "equipment_num" TYPE int8;

-- 删除表 wms_discard_order_details 中的字段 enter_repository_time
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: enter_repository_time
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "enter_repository_time";

-- 删除表 wms_discard_order_details 中的字段 order_no
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: order_no
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "order_no";

-- 删除表 wms_discard_order_details 中的字段 discard_order_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: discard_order_id
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "discard_order_id";

-- 删除表 wms_discard_order_details 中的字段 price
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: price
ALTER TABLE "public"."wms_discard_order_details" DROP COLUMN "price";

-- 修改表 wms_discard_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: num
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_order_details 中的字段 reason
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: reason
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "reason" TYPE text COLLATE "pg_catalog"."default";
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "reason" DROP NOT NULL;

-- 修改表 wms_discard_order_details 中的字段 discard_time
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: discard_time
ALTER TABLE "public"."wms_discard_order_details" ALTER COLUMN "discard_time" DROP NOT NULL;

-- 在表 wms_discard_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."order_id" IS '报废单';

-- 在表 wms_discard_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."material_id" IS '物料';

-- 在表 wms_discard_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."material_name" IS '物料名称';

-- 在表 wms_discard_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_discard_order_details"."feature" IS '详细规格';

-- 在表 wms_discard_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_discard_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_discard_order_details"."owner_id" IS '归属人';

-- 删除表 wms_discard_orders 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: source
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "source";

-- 删除表 wms_discard_orders 中的字段 source_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: source_id
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "source_id";

-- 删除表 wms_discard_orders 中的字段 workflow_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: workflow_id
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "workflow_id";

-- 删除表 wms_discard_orders 中的字段 fire_station_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_discard_orders
-- 字段名: fire_station_id
ALTER TABLE "public"."wms_discard_orders" DROP COLUMN "fire_station_id";

-- 在表 wms_discard_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_orders
-- 字段名: status
ALTER TABLE "public"."wms_discard_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default" NOT NULL;
COMMENT ON COLUMN "public"."wms_discard_orders"."status" IS '报废状态';

-- 在表 wms_discard_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_discard_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_discard_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_discard_orders"."equipment_num" IS '设备数量';

-- 修改表 wms_discard_plan_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_plan_order_details
-- 字段名: num
ALTER TABLE "public"."wms_discard_plan_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_discard_plan_orders 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_plan_orders
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_discard_plan_orders" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_discard_plan_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_discard_plan_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_discard_plan_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_enter_repository_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_enter_repository_order_details
-- 字段名: num
ALTER TABLE "public"."wms_enter_repository_order_details" ALTER COLUMN "num" TYPE int8;

-- 在表 wms_enter_repository_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_enter_repository_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_enter_repository_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_enter_repository_order_details"."material_id" IS '资产id';

-- 修改表 wms_enter_repository_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_enter_repository_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_enter_repository_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 在表 wms_equipment_type_properties 上添加主键 (id)
-- 操作类型: ADD_PRIMARY_KEY
-- 表名: wms_equipment_type_properties
ALTER TABLE "public"."wms_equipment_type_properties" ADD PRIMARY KEY ("id");

-- 在表 wms_equipment_type_property_groups 上添加主键 (id)
-- 操作类型: ADD_PRIMARY_KEY
-- 表名: wms_equipment_type_property_groups
ALTER TABLE "public"."wms_equipment_type_property_groups" ADD PRIMARY KEY ("id");

-- 在表 wms_equipment_type_property_options 上创建索引 wmsequipmenttypepropertyoption_equipment_type_property_id_code
-- 操作类型: ADD_INDEX
-- 表名: wms_equipment_type_property_options
-- 索引名: wmsequipmenttypepropertyoption_equipment_type_property_id_code
CREATE UNIQUE INDEX "wmsequipmenttypepropertyoption_equipment_type_property_id_code" ON "public"."wms_equipment_type_property_options" ("code", "equipment_type_property_id");

-- 删除索引 wmsequipmenttypepropertyoption_equipment_type_property_id_code
-- 操作类型: DROP_INDEX
-- 表名: wms_equipment_type_property_options_bak
-- 索引名: wmsequipmenttypepropertyoption_equipment_type_property_id_code
DROP INDEX "public"."wmsequipmenttypepropertyoption_equipment_type_property_id_code";

-- 在表 wms_equipment_types 上创建索引 wmsequipmenttype_code
-- 操作类型: ADD_INDEX
-- 表名: wms_equipment_types
-- 索引名: wmsequipmenttype_code
CREATE UNIQUE INDEX "wmsequipmenttype_code" ON "public"."wms_equipment_types" ("code");

-- 删除索引 wmsequipmenttype_code
-- 操作类型: DROP_INDEX
-- 表名: wms_equipment_types_bak
-- 索引名: wmsequipmenttype_code
DROP INDEX "public"."wmsequipmenttype_code";

-- 修改表 wms_learning_course_records 中的字段 process
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_course_records
-- 字段名: process
ALTER TABLE "public"."wms_learning_course_records" ALTER COLUMN "process" TYPE int8;

-- 修改表 wms_learning_courseware_records 中的字段 process
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_courseware_records
-- 字段名: process
ALTER TABLE "public"."wms_learning_courseware_records" ALTER COLUMN "process" TYPE int8;

-- 修改表 wms_learning_coursewares 中的字段 time_length
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_coursewares
-- 字段名: time_length
ALTER TABLE "public"."wms_learning_coursewares" ALTER COLUMN "time_length" TYPE int8;

-- 修改表 wms_learning_plan_records 中的字段 process
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_learning_plan_records
-- 字段名: process
ALTER TABLE "public"."wms_learning_plan_records" ALTER COLUMN "process" TYPE int8;

-- 删除表 wms_maintain_order_details 中的字段 enter_repository_time
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: enter_repository_time
ALTER TABLE "public"."wms_maintain_order_details" DROP COLUMN "enter_repository_time";

-- 删除表 wms_maintain_order_details 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: source
ALTER TABLE "public"."wms_maintain_order_details" DROP COLUMN "source";

-- 删除表 wms_maintain_order_details 中的字段 maintain_order_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: maintain_order_id
ALTER TABLE "public"."wms_maintain_order_details" DROP COLUMN "maintain_order_id";

-- 修改表 wms_maintain_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: num
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_maintain_order_details 中的字段 reason
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: reason
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "reason" TYPE text COLLATE "pg_catalog"."default";
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "reason" DROP NOT NULL;

-- 修改表 wms_maintain_order_details 中的字段 maintain_time
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: maintain_time
ALTER TABLE "public"."wms_maintain_order_details" ALTER COLUMN "maintain_time" DROP NOT NULL;

-- 在表 wms_maintain_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."order_id" IS '保养单';

-- 在表 wms_maintain_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."material_id" IS '物料';

-- 在表 wms_maintain_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."material_name" IS '物料名称';

-- 在表 wms_maintain_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_maintain_order_details"."feature" IS '详细规格';

-- 在表 wms_maintain_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."owner_id" IS '归属人';

-- 在表 wms_maintain_order_details 中添加字段 complete_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: complete_time
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "complete_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_maintain_order_details"."complete_time" IS '完成时间';

-- 在表 wms_maintain_order_details 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: status
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "status" int4 DEFAULT '0';
COMMENT ON COLUMN "public"."wms_maintain_order_details"."status" IS '状态';

-- 在表 wms_maintain_order_details 中添加字段 files
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: files
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "files" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."files" IS '附件';

-- 在表 wms_maintain_order_details 中添加字段 complete_extra_info
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: complete_extra_info
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "complete_extra_info" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_order_details"."complete_extra_info" IS '完成的补充说明';

-- 在表 wms_maintain_order_details 中添加字段 order_status
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_order_details
-- 字段名: order_status
ALTER TABLE "public"."wms_maintain_order_details" ADD COLUMN "order_status" varchar COLLATE "pg_catalog"."default" DEFAULT '0'::character varying;
COMMENT ON COLUMN "public"."wms_maintain_order_details"."order_status" IS '订单状态';

-- 删除表 wms_maintain_orders 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: source
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "source";

-- 删除表 wms_maintain_orders 中的字段 source_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: source_id
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "source_id";

-- 删除表 wms_maintain_orders 中的字段 workflow_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: workflow_id
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "workflow_id";

-- 删除表 wms_maintain_orders 中的字段 fire_station_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: fire_station_id
ALTER TABLE "public"."wms_maintain_orders" DROP COLUMN "fire_station_id";

-- 在表 wms_maintain_orders 中添加字段 contract_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: contract_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "contract_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."contract_urls" IS '合同附件';

-- 在表 wms_maintain_orders 中添加字段 invoice_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: invoice_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "invoice_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."invoice_urls" IS '发票附件';

-- 在表 wms_maintain_orders 中添加字段 audit_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: audit_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "audit_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."audit_urls" IS '会审凭证附件';

-- 在表 wms_maintain_orders 中添加字段 other_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: other_urls
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "other_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."other_urls" IS '会审凭证附件';

-- 在表 wms_maintain_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_maintain_orders"."equipment_num" IS '装备数量';

-- 在表 wms_maintain_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: status
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_maintain_orders"."status" IS '保养状态';

-- 在表 wms_maintain_orders 中添加字段 maintain_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_maintain_orders
-- 字段名: maintain_time
ALTER TABLE "public"."wms_maintain_orders" ADD COLUMN "maintain_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_maintain_orders"."maintain_time" IS '保养时间';

-- 修改表 wms_maintain_plan_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_plan_details
-- 字段名: num
ALTER TABLE "public"."wms_maintain_plan_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_maintain_plans 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_plans
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_maintain_plans" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_maintain_plans 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_maintain_plans
-- 字段名: equipment_num
ALTER TABLE "public"."wms_maintain_plans" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_material_logs 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_material_logs
-- 字段名: num
ALTER TABLE "public"."wms_material_logs" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_materials 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_materials
-- 字段名: num
ALTER TABLE "public"."wms_materials" ALTER COLUMN "num" TYPE int8;

-- 在表 wms_materials 中添加字段 finance_system_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_materials
-- 字段名: finance_system_id
ALTER TABLE "public"."wms_materials" ADD COLUMN "finance_system_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_materials"."finance_system_id" IS '财务系统id';

-- 在表 wms_materials 中添加字段 use_start_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_materials
-- 字段名: use_start_time
ALTER TABLE "public"."wms_materials" ADD COLUMN "use_start_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_materials"."use_start_time" IS '使用开始时间';

-- 在表 wms_materials 中添加字段 finance_system_no
-- 操作类型: ADD_COLUMN
-- 表名: wms_materials
-- 字段名: finance_system_no
ALTER TABLE "public"."wms_materials" ADD COLUMN "finance_system_no" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_materials"."finance_system_no" IS '财务系统编号';

-- 修改表 wms_out_repository_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_out_repository_order_details
-- 字段名: num
ALTER TABLE "public"."wms_out_repository_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_out_repository_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_out_repository_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_out_repository_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 修改表 wms_purchase_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_purchase_order_details
-- 字段名: num
ALTER TABLE "public"."wms_purchase_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_purchase_orders 中的字段 equipment_type_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_purchase_orders
-- 字段名: equipment_type_num
ALTER TABLE "public"."wms_purchase_orders" ALTER COLUMN "equipment_type_num" TYPE int8;

-- 修改表 wms_purchase_orders 中的字段 equipment_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_purchase_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_purchase_orders" ALTER COLUMN "equipment_num" TYPE int8;

-- 在表 wms_repair_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."order_id" IS '维修单';

-- 在表 wms_repair_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."material_id" IS '物料';

-- 在表 wms_repair_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."material_name" IS '物料名称';

-- 在表 wms_repair_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_repair_order_details"."feature" IS '详细规格';

-- 在表 wms_repair_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."owner_id" IS '归属人';

-- 在表 wms_repair_order_details 中添加字段 measure_unit_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: measure_unit_id
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "measure_unit_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."measure_unit_id" IS '计量单位';

-- 在表 wms_repair_order_details 中添加字段 num
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: num
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "num" int8 NOT NULL;
COMMENT ON COLUMN "public"."wms_repair_order_details"."num" IS '维修数量';

-- 在表 wms_repair_order_details 中添加字段 repair_type
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: repair_type
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "repair_type" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."repair_type" IS '维修类型';

-- 在表 wms_repair_order_details 中添加字段 repair_reason
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: repair_reason
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "repair_reason" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."repair_reason" IS '故障描述';

-- 在表 wms_repair_order_details 中添加字段 image_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: image_urls
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "image_urls" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."image_urls" IS '故障图片';

-- 在表 wms_repair_order_details 中添加字段 repair_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: repair_time
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "repair_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_repair_order_details"."repair_time" IS '维修时间';

-- 在表 wms_repair_order_details 中添加字段 complete_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: complete_time
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "complete_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_repair_order_details"."complete_time" IS '完成时间';

-- 在表 wms_repair_order_details 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: status
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "status" int4 DEFAULT '0';
COMMENT ON COLUMN "public"."wms_repair_order_details"."status" IS '状态';

-- 在表 wms_repair_order_details 中添加字段 files
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: files
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "files" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."files" IS '附件';

-- 在表 wms_repair_order_details 中添加字段 complete_extra_info
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: complete_extra_info
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "complete_extra_info" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_order_details"."complete_extra_info" IS '完成的补充说明';

-- 在表 wms_repair_order_details 中添加字段 order_status
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_order_details
-- 字段名: order_status
ALTER TABLE "public"."wms_repair_order_details" ADD COLUMN "order_status" varchar COLLATE "pg_catalog"."default" DEFAULT '0'::character varying;
COMMENT ON COLUMN "public"."wms_repair_order_details"."order_status" IS '订单状态';

-- 在表 wms_repair_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_repair_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_repair_orders"."equipment_num" IS '装备数量';

-- 在表 wms_repair_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_orders
-- 字段名: status
ALTER TABLE "public"."wms_repair_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_repair_orders"."status" IS '维修状态';

-- 在表 wms_repair_orders 中添加字段 repair_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_repair_orders
-- 字段名: repair_time
ALTER TABLE "public"."wms_repair_orders" ADD COLUMN "repair_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_repair_orders"."repair_time" IS '维修时间';

-- 修改表 wms_repair_settlement_order_workfee_details 中的字段 hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_order_workfee_details
-- 字段名: hours
ALTER TABLE "public"."wms_repair_settlement_order_workfee_details" ALTER COLUMN "hours" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 work_hours_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: work_hours_standard
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "work_hours_standard" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 execute_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: execute_standard
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "execute_standard" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 total_work_hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: total_work_hours
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "total_work_hours" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 is_old_confirm_and_recover
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: is_old_confirm_and_recover
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "is_old_confirm_and_recover" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 is_old_confirm_and_giveup
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: is_old_confirm_and_giveup
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "is_old_confirm_and_giveup" TYPE int8;

-- 修改表 wms_repair_settlement_orders 中的字段 is_none_old
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repair_settlement_orders
-- 字段名: is_none_old
ALTER TABLE "public"."wms_repair_settlement_orders" ALTER COLUMN "is_none_old" TYPE int8;

-- 修改表 wms_repository_screens 中的字段 equipment_type_carousel_interval
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repository_screens
-- 字段名: equipment_type_carousel_interval
ALTER TABLE "public"."wms_repository_screens" ALTER COLUMN "equipment_type_carousel_interval" TYPE int8;

-- 修改表 wms_repository_screens 中的字段 equipment_type_sub_carousel_interval
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repository_screens
-- 字段名: equipment_type_sub_carousel_interval
ALTER TABLE "public"."wms_repository_screens" ALTER COLUMN "equipment_type_sub_carousel_interval" TYPE int8;

-- 修改表 wms_repository_screens 中的字段 material_carousel_interval
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_repository_screens
-- 字段名: material_carousel_interval
ALTER TABLE "public"."wms_repository_screens" ALTER COLUMN "material_carousel_interval" TYPE int8;

-- 删除表 wms_return_order_details 中的字段 enter_repository_time
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: enter_repository_time
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "enter_repository_time";

-- 删除表 wms_return_order_details 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: source
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "source";

-- 删除表 wms_return_order_details 中的字段 return_order_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: return_order_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "return_order_id";

-- 删除表 wms_return_order_details 中的字段 repository_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: repository_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "repository_id";

-- 删除表 wms_return_order_details 中的字段 repository_area_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: repository_area_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "repository_area_id";

-- 删除表 wms_return_order_details 中的字段 repository_position_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_order_details
-- 字段名: repository_position_id
ALTER TABLE "public"."wms_return_order_details" DROP COLUMN "repository_position_id";

-- 修改表 wms_return_order_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_return_order_details
-- 字段名: num
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_return_order_details 中的字段 reason
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_return_order_details
-- 字段名: reason
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "reason" TYPE text COLLATE "pg_catalog"."default";
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "reason" DROP NOT NULL;

-- 修改表 wms_return_order_details 中的字段 return_time
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_return_order_details
-- 字段名: return_time
ALTER TABLE "public"."wms_return_order_details" ALTER COLUMN "return_time" DROP NOT NULL;

-- 在表 wms_return_order_details 中添加字段 order_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: order_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "order_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."order_id" IS '退还单';

-- 在表 wms_return_order_details 中添加字段 material_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: material_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "material_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."material_id" IS '物料';

-- 在表 wms_return_order_details 中添加字段 material_name
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: material_name
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "material_name" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."material_name" IS '物料名称';

-- 在表 wms_return_order_details 中添加字段 feature
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: feature
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "feature" jsonb;
COMMENT ON COLUMN "public"."wms_return_order_details"."feature" IS '详细规格';

-- 在表 wms_return_order_details 中添加字段 owner_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: owner_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "owner_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."owner_id" IS '归属人';

-- 在表 wms_return_order_details 中添加字段 to_repository_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: to_repository_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "to_repository_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."to_repository_id" IS '仓库';

-- 在表 wms_return_order_details 中添加字段 to_repository_area_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: to_repository_area_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "to_repository_area_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."to_repository_area_id" IS '库区';

-- 在表 wms_return_order_details 中添加字段 to_repository_position_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_order_details
-- 字段名: to_repository_position_id
ALTER TABLE "public"."wms_return_order_details" ADD COLUMN "to_repository_position_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_order_details"."to_repository_position_id" IS '库位';

-- 删除表 wms_return_orders 中的字段 source
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: source
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "source";

-- 删除表 wms_return_orders 中的字段 source_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: source_id
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "source_id";

-- 删除表 wms_return_orders 中的字段 workflow_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: workflow_id
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "workflow_id";

-- 删除表 wms_return_orders 中的字段 fire_station_id
-- 操作类型: DROP_COLUMN
-- 表名: wms_return_orders
-- 字段名: fire_station_id
ALTER TABLE "public"."wms_return_orders" DROP COLUMN "fire_station_id";

-- 在表 wms_return_orders 中添加字段 contract_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: contract_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "contract_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."contract_urls" IS '合同附件';

-- 在表 wms_return_orders 中添加字段 invoice_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: invoice_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "invoice_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."invoice_urls" IS '发票附件';

-- 在表 wms_return_orders 中添加字段 audit_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: audit_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "audit_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."audit_urls" IS '会审凭证附件';

-- 在表 wms_return_orders 中添加字段 other_urls
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: other_urls
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "other_urls" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."other_urls" IS '会审凭证附件';

-- 在表 wms_return_orders 中添加字段 repository_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: repository_id
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "repository_id" varchar COLLATE "pg_catalog"."default" NOT NULL;
COMMENT ON COLUMN "public"."wms_return_orders"."repository_id" IS '仓库id';

-- 在表 wms_return_orders 中添加字段 reason
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: reason
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "reason" text COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."reason" IS '退还原因';

-- 在表 wms_return_orders 中添加字段 equipment_num
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: equipment_num
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "equipment_num" int8;
COMMENT ON COLUMN "public"."wms_return_orders"."equipment_num" IS '装备数量';

-- 在表 wms_return_orders 中添加字段 status
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: status
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "status" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_return_orders"."status" IS '退还状态';

-- 在表 wms_return_orders 中添加字段 return_time
-- 操作类型: ADD_COLUMN
-- 表名: wms_return_orders
-- 字段名: return_time
ALTER TABLE "public"."wms_return_orders" ADD COLUMN "return_time" timestamptz(6);
COMMENT ON COLUMN "public"."wms_return_orders"."return_time" IS '退还时间';

-- 在表 wms_rfid_readers 中添加字段 alarm_user_id
-- 操作类型: ADD_COLUMN
-- 表名: wms_rfid_readers
-- 字段名: alarm_user_id
ALTER TABLE "public"."wms_rfid_readers" ADD COLUMN "alarm_user_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."wms_rfid_readers"."alarm_user_id" IS '报警用户';

-- 修改表 wms_vehicle_repair_order_materialfee_details 中的字段 num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_order_materialfee_details
-- 字段名: num
ALTER TABLE "public"."wms_vehicle_repair_order_materialfee_details" ALTER COLUMN "num" TYPE int8;

-- 修改表 wms_vehicle_repair_order_workfee_details 中的字段 hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_order_workfee_details
-- 字段名: hours
ALTER TABLE "public"."wms_vehicle_repair_order_workfee_details" ALTER COLUMN "hours" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 work_hours_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: work_hours_standard
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "work_hours_standard" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 execute_standard
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: execute_standard
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "execute_standard" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 total_work_hours
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: total_work_hours
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "total_work_hours" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 total_material_num
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: total_material_num
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "total_material_num" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 is_old_confirm_and_recover
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: is_old_confirm_and_recover
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "is_old_confirm_and_recover" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 is_old_confirm_and_giveup
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: is_old_confirm_and_giveup
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "is_old_confirm_and_giveup" TYPE int8;

-- 修改表 wms_vehicle_repair_orders 中的字段 is_none_old
-- 操作类型: MODIFY_COLUMN
-- 表名: wms_vehicle_repair_orders
-- 字段名: is_none_old
ALTER TABLE "public"."wms_vehicle_repair_orders" ALTER COLUMN "is_none_old" TYPE int8;
