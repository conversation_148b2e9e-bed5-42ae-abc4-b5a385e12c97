#!/usr/bin/env node

// 数据库连接测试脚本
import { MySQLDriver } from './dist/database/driver.js';
import { loadConfig } from './dist/utils/config.js';

console.log('🧪 测试数据库连接...\n');

async function testConnection() {
  try {
    // 加载配置文件
    console.log('📁 加载配置文件...');
    const config = loadConfig('config.test.yaml');
    
    const driver = new MySQLDriver();
    
    for (const [name, dbConfig] of Object.entries(config)) {
      console.log(`\n🔍 测试数据库: ${name}`);
      console.log(`   驱动: ${dbConfig.driver}`);
      
      if (dbConfig.source) {
        console.log(`   连接字符串: ${dbConfig.source.substring(0, 30)}...`);
      } else {
        console.log(`   主机: ${dbConfig.host}:${dbConfig.port || 3306}`);
        console.log(`   数据库: ${dbConfig.db}`);
        console.log(`   用户: ${dbConfig.user}`);
      }
      
      try {
        console.log('   🔌 尝试连接...');
        
        // 测试连接（设置较短的超时时间）
        const connection = await Promise.race([
          driver.connect(dbConfig),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('连接超时')), 5000)
          )
        ]);
        
        console.log('   ✅ 连接成功！');
        
        // 测试简单查询
        try {
          console.log('   📊 测试查询...');
          const result = await driver.query(connection, 'SELECT 1 as test');
          console.log(`   ✅ 查询成功！返回 ${result.rows.length} 行数据`);
          
          if (result.rows.length > 0) {
            console.log(`   📋 结果: ${JSON.stringify(result.rows[0])}`);
          }
        } catch (queryError) {
          console.log(`   ⚠️  查询失败: ${queryError.message}`);
        }
        
        // 关闭连接
        await driver.close(connection);
        console.log('   🔒 连接已关闭');
        
      } catch (error) {
        console.log(`   ❌ 连接失败: ${error.message}`);
        
        // 提供一些常见问题的解决建议
        if (error.message.includes('ECONNREFUSED')) {
          console.log('   💡 建议: 检查数据库服务是否启动，主机和端口是否正确');
        } else if (error.message.includes('Access denied')) {
          console.log('   💡 建议: 检查用户名和密码是否正确');
        } else if (error.message.includes('Unknown database')) {
          console.log('   💡 建议: 检查数据库名称是否存在');
        } else if (error.message.includes('连接超时')) {
          console.log('   💡 建议: 检查网络连接和防火墙设置');
        }
      }
    }
    
    console.log('\n🎉 数据库连接测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
testConnection();
