#!/usr/bin/env node

// 测试 PostgreSQL geometry 类型元数据获取
import { createMetadata } from './dist/database/metadata.js';

console.log('🧪 测试 PostgreSQL geometry 类型元数据获取...\n');

const config = {
  driver: 'postgresql',
  host: '***************',
  port: 5432,
  user: 'postgres',
  password: '123456',
  db: 'demo'
};

async function testGeometryMetadata() {
  try {
    console.log('🔌 连接数据库...');
    const metadata = await createMetadata(config);
    console.log('✅ 连接成功！\n');
    
    console.log('📊 获取表列表...');
    const tables = await metadata.getTables();
    console.log(`找到 ${tables.length} 个表\n`);
    
    // 查找包含 geometry 字段的表
    for (const tableName of tables) {
      console.log(`🔍 检查表: ${tableName}`);
      const tableInfo = await metadata.getTableInfo(tableName);
      
      const geometryColumns = tableInfo.columns.filter(col => 
        col.type.toLowerCase().includes('geometry')
      );
      
      if (geometryColumns.length > 0) {
        console.log(`  ✅ 找到 geometry 字段:`);
        geometryColumns.forEach(col => {
          console.log(`    - ${col.name}: ${col.type}`);
        });
      }
    }
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    console.log('错误详情:', error);
  }
}

testGeometryMetadata();
