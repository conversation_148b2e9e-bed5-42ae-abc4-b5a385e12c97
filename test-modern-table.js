#!/usr/bin/env node

// 现代化表格功能测试
console.log('🎨 测试现代化表格功能...\n');

// 模拟大量数据来测试分页功能
const generateMockData = (rows = 25) => {
  const columns = [
    'id', 'name', 'email', 'phone', 'address', 'city', 'country', 
    'postal_code', 'created_at', 'updated_at', 'status', 'notes'
  ];

  const data = [];
  const statuses = ['active', 'inactive', 'pending', 'suspended'];
  const cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia'];
  const countries = ['USA', 'Canada', 'UK', 'Germany', 'France', 'Japan'];

  for (let i = 1; i <= rows; i++) {
    data.push([
      i.toString(),
      `User ${i}`,
      `user${i}@example.com`,
      `******-${String(i).padStart(4, '0')}`,
      `${100 + i} Main Street`,
      cities[i % cities.length],
      countries[i % countries.length],
      `${10000 + i}`,
      `2023-${String((i % 12) + 1).padStart(2, '0')}-${String((i % 28) + 1).padStart(2, '0')} 10:30:00`,
      `2023-12-${String((i % 28) + 1).padStart(2, '0')} 14:20:00`,
      statuses[i % statuses.length],
      i % 3 === 0 ? 'VIP customer' : i % 5 === 0 ? 'Regular customer' : 'Standard user'
    ]);
  }

  return { columns, rows: data };
};

// 测试分页功能
function testPagination() {
  console.log('📄 分页功能测试:');
  
  const { columns, rows } = generateMockData(25);
  const ROWS_PER_PAGE = 10;
  const totalPages = Math.ceil(rows.length / ROWS_PER_PAGE);
  
  console.log(`   总数据: ${rows.length} 行`);
  console.log(`   每页显示: ${ROWS_PER_PAGE} 行`);
  console.log(`   总页数: ${totalPages} 页\n`);
  
  for (let page = 0; page < totalPages; page++) {
    const startRow = page * ROWS_PER_PAGE;
    const endRow = Math.min(startRow + ROWS_PER_PAGE, rows.length);
    const pageRows = rows.slice(startRow, endRow);
    
    console.log(`   第 ${page + 1} 页 (行 ${startRow + 1}-${endRow}):`);
    console.log(`     显示行数: ${pageRows.length}`);
    console.log(`     第一行ID: ${pageRows[0][0]}`);
    console.log(`     最后行ID: ${pageRows[pageRows.length - 1][0]}`);
    console.log('');
  }
}

// 测试列宽计算
function testColumnWidths() {
  console.log('📏 列宽计算测试:');
  
  const { columns, rows } = generateMockData(10);
  
  const columnWidths = columns.map((col, index) => {
    const headerWidth = col.length;
    const dataWidth = Math.max(
      ...rows.map(row => (row[index] || '').toString().length)
    );
    return Math.min(Math.max(headerWidth, dataWidth, 6), 20);
  });
  
  console.log('   列名 -> 计算宽度:');
  columns.forEach((col, index) => {
    console.log(`     ${col.padEnd(12)} -> ${columnWidths[index]} 字符`);
  });
  console.log('');
}

// 测试响应式显示
function testResponsiveDisplay() {
  console.log('📱 响应式显示测试:');
  
  const { columns } = generateMockData(5);
  const columnWidths = [2, 8, 20, 12, 15, 10, 7, 10, 19, 19, 8, 12]; // 模拟计算的列宽
  
  const testWidths = [60, 80, 120, 160];
  
  testWidths.forEach(terminalWidth => {
    const availableWidth = terminalWidth - 6;
    let visibleColumns = [];
    let currentWidth = 0;
    
    // 从第一列开始添加，直到超出可用宽度
    for (let i = 0; i < columns.length; i++) {
      if (currentWidth + columnWidths[i] + 1 <= availableWidth) {
        visibleColumns.push(columns[i]);
        currentWidth += columnWidths[i] + 1;
      } else {
        break;
      }
    }
    
    console.log(`   终端宽度 ${terminalWidth}:`);
    console.log(`     可用宽度: ${availableWidth}`);
    console.log(`     可见列数: ${visibleColumns.length}/${columns.length}`);
    console.log(`     可见列: ${visibleColumns.join(', ')}`);
    console.log(`     使用宽度: ${currentWidth}/${availableWidth}`);
    console.log('');
  });
}

// 测试键盘导航
function testKeyboardNavigation() {
  console.log('⌨️  键盘导航测试:');
  
  const { columns, rows } = generateMockData(25);
  const ROWS_PER_PAGE = 10;
  const totalPages = Math.ceil(rows.length / ROWS_PER_PAGE);
  
  // 模拟导航场景
  const scenarios = [
    {
      name: '向下导航到页面边界',
      currentRow: 9,
      currentPage: 0,
      action: 'down',
      expectedRow: 10,
      expectedPage: 1
    },
    {
      name: '向上导航到页面边界',
      currentRow: 10,
      currentPage: 1,
      action: 'up',
      expectedRow: 9,
      expectedPage: 0
    },
    {
      name: 'Page Down',
      currentRow: 5,
      currentPage: 0,
      action: 'pageDown',
      expectedRow: 10,
      expectedPage: 1
    },
    {
      name: 'Page Up',
      currentRow: 15,
      currentPage: 1,
      action: 'pageUp',
      expectedRow: 9,
      expectedPage: 0
    }
  ];
  
  scenarios.forEach(scenario => {
    console.log(`   ${scenario.name}:`);
    console.log(`     当前: 第${scenario.currentPage + 1}页, 行${scenario.currentRow + 1}`);
    console.log(`     操作: ${scenario.action}`);
    console.log(`     预期: 第${scenario.expectedPage + 1}页, 行${scenario.expectedRow + 1}`);
    console.log('');
  });
}

// 运行所有测试
console.log('🚀 开始现代化表格功能测试...\n');

testPagination();
testColumnWidths();
testResponsiveDisplay();
testKeyboardNavigation();

console.log('🎯 新功能特性:');
console.log('✅ 分页显示 - 每页最多10行，支持翻页');
console.log('✅ 紧凑设计 - 减少列间距，更高效利用空间');
console.log('✅ 现代化UI - 清晰的边框和状态指示');
console.log('✅ 智能导航 - 跨页面的行导航');
console.log('✅ 状态反馈 - 实时显示页码和位置信息');
console.log('');

console.log('🎮 增强的交互:');
console.log('• ↑↓ / j k: 行导航（自动跨页）');
console.log('• ←→ / h l: 列导航（循环滚动）');
console.log('• PgUp/PgDn: 快速翻页');
console.log('• v: 查看列详情');
console.log('• Tab: 切换焦点');
console.log('');

console.log('🎨 UI 改进:');
console.log('• 紧凑的列间距');
console.log('• 清晰的表格边框');
console.log('• 现代化的状态栏');
console.log('• 智能的内容截断');
console.log('• 分页信息显示');
console.log('');

console.log('🎉 现代化表格功能测试完成！');
console.log('💡 在实际应用中体验更流畅的数据浏览');
