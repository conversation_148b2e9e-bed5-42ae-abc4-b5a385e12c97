# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

DBTool 是一个用Go编写的数据库命令行工具，支持数据库连接测试和现代化TUI查询界面。项目使用Cobra作为CLI框架，Charm Bubbles/tview作为TUI界面，目前主要支持MySQL数据库。

## 常用命令

### 编译和构建
```bash
go mod download
go build -o dbtool
```

### 运行命令
```bash
# 测试数据库连接
./dbtool test db1

# 使用指定配置文件
./dbtool -c /path/to/config.yaml test db1

# 打开TUI查询界面
./dbtool query
```

### 开发和调试
```bash
# 运行（开发时）
go run main.go query
go run main.go test db1

# 格式化代码
go fmt ./...

# 模块整理
go mod tidy
```

## 架构结构

### 核心组件
- **cmd/**: CLI命令定义，基于Cobra框架
  - `root.go`: 根命令和全局配置
  - `test.go`: 数据库连接测试命令
  - `query.go`: TUI查询界面启动命令
- **internal/config/**: 配置文件处理，支持YAML格式
- **internal/database/**: 数据库驱动抽象层
- **internal/tui/**: TUI界面实现，基于tview和Charm Bubbles

### 配置系统
配置文件支持两种格式：
1. **连接字符串格式**（推荐）：使用`source`字段直接指定完整连接串
2. **分段配置格式**：分别指定host、port、user、password等字段

### TUI界面架构
- **三面板布局**：左侧数据库/表列表，右上SQL输入框，右下查询结果表格
- **Vim风格命令系统**：使用`:`进入命令模式，支持Tab补全和历史记录
- **异步查询执行**：数据库操作在goroutine中执行，避免界面阻塞

## TUI命令系统

界面支持以下Vim风格命令：
- `:query` - 聚焦到SQL查询输入框
- `:db` - 聚焦到数据库选择列表
- `:table` - 聚焦到表列表
- `:commit` - 执行当前SQL查询
- `:quit` - 退出TUI界面

命令系统特性：
- Tab自动补全
- 上下键浏览历史记录
- 实时匹配提示

## 数据库支持

当前支持MySQL，架构设计为可扩展的驱动系统。添加新数据库类型需要：
1. 在`internal/database/`添加新驱动实现
2. 实现`Driver`接口的`Connect`和`TestConnection`方法
3. 在`GetDriver`函数中注册新驱动

## 重要注意事项

- 配置文件中包含敏感信息（数据库密码），不应提交到版本控制
- 查询操作有30秒超时限制
- TUI界面使用goroutine处理数据库操作，确保界面响应性
- 支持水平滚动和固定列宽（20字符）的结果显示