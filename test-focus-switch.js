#!/usr/bin/env node

/**
 * 测试查询完成后焦点自动切换功能
 */

const React = require('react');
const { render, Box, Text, useInput } = require('ink');
const { App } = require('./dist/components/App.js');

// 模拟配置
const testConfig = {
  test_db: {
    driver: 'sqlite',
    source: ':memory:'
  }
};

function TestApp() {
  const [step, setStep] = useState(0);
  const [message, setMessage] = useState('开始测试...');

  useInput((input, key) => {
    if (key.ctrl && input === 'c') {
      process.exit(0);
    }
    
    if (input === 'n') {
      setStep(prev => prev + 1);
    }
  });

  useEffect(() => {
    const messages = [
      '1. 启动应用，应该自动选择数据库并进入查询输入状态',
      '2. 输入查询语句并执行',
      '3. 查询完成后，焦点应该自动切换到结果表格',
      '4. 可以使用方向键导航结果表格',
      '5. 按 Tab 键可以在不同组件间切换焦点'
    ];
    
    if (step < messages.length) {
      setMessage(messages[step]);
    }
  }, [step]);

  if (step >= 5) {
    return (
      <Box flexDirection="column">
        <Text color="green">✅ 测试完成！</Text>
        <Text>现在启动实际应用进行测试...</Text>
        <App config={testConfig} />
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      <Box borderStyle="round" padding={1} marginBottom={1}>
        <Text bold color="cyan">焦点自动切换功能测试</Text>
      </Box>
      
      <Box marginBottom={1}>
        <Text>步骤 {step + 1}/5: {message}</Text>
      </Box>
      
      <Box>
        <Text color="gray">按 'n' 继续下一步，Ctrl+C 退出</Text>
      </Box>
      
      {step === 4 && (
        <Box marginTop={1}>
          <App config={testConfig} />
        </Box>
      )}
    </Box>
  );
}

render(<TestApp />);
