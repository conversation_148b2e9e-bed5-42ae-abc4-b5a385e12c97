{"name": "dbtool-ink", "version": "1.0.0", "description": "Database query tool built with ink and TypeScript", "type": "module", "main": "dist/index.js", "bin": {"dbtool": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.tsx", "watch": "tsc --watch", "clean": "rm -rf dist", "test-config": "node test-config.js", "test-connection": "node test-connection.js", "test-db": "node test-db-connection.js", "test-table": "node test-table-simple.js", "test-modern-table": "node test-modern-table.js", "test-postgresql": "node test-postgresql.js", "test-migrate": "node test-migrate.js"}, "keywords": ["database", "query", "cli", "tui", "ink", "typescript"], "author": "", "license": "MIT", "dependencies": {"@types/pg": "^8.15.5", "chalk": "^5.3.0", "ink": "^5.0.1", "ink-select-input": "^6.0.0", "ink-text-input": "^6.0.0", "mysql2": "^3.11.4", "pg": "^8.16.3", "react": "^18.3.1", "yaml": "^2.6.1"}, "devDependencies": {"@types/node": "^22.10.5", "@types/react": "^18.3.17", "tsx": "^4.19.2", "typescript": "^5.7.3"}, "engines": {"node": ">=18.0.0"}}