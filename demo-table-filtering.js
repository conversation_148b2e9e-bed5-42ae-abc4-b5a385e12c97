#!/usr/bin/env node

/**
 * 表排除功能演示脚本
 * 演示如何在配置和命令行中使用表过滤功能
 */

console.log('🎯 DBTool 表排除功能演示');
console.log('==========================');
console.log('');

console.log('📋 支持的过滤方式:');
console.log('');

console.log('1️⃣ 配置文件中的表过滤');
console.log('------------------------');
console.log(`
# config.yaml 示例
source_db:
  driver: postgresql
  host: localhost
  port: 5432
  db: production
  user: postgres
  password: password
  # 排除系统表和日志表
  excludeTables:
    - spatial_ref_sys
    - geometry_columns
    - access_logs
    - error_logs
    - sessions

target_db:
  driver: mysql
  host: localhost
  port: 3306
  db: production_mysql
  user: root
  password: password
  # 只包含核心业务表
  includeTables:
    - users
    - products
    - orders
    - categories
    - inventory
`);

console.log('2️⃣ 命令行参数过滤');
console.log('--------------------');
console.log(`
# 排除特定表（会覆盖配置文件设置）
dbtool migrate --exclude logs,sessions,temp_data source_db target_db ./migrations

# 仅包含特定表（会覆盖配置文件设置）
dbtool migrate --include users,products,orders source_db target_db ./migrations

# 同时使用包含和排除
dbtool migrate --include users,products,orders,logs --exclude logs source_db target_db ./migrations

# 结合其他选项
dbtool migrate --include-data --exclude temp_table,log_table source_db target_db ./migrations
`);

console.log('3️⃣ 过滤规则优先级');
console.log('--------------------');
console.log('📈 优先级（从高到低）:');
console.log('   1. 命令行 --include / --exclude 参数（最高优先级）');
console.log('   2. 配置文件中的 includeTables');
console.log('   3. 配置文件中的 excludeTables');
console.log('');
console.log('🔄 处理逻辑:');
console.log('   1. 获取数据库中的所有表');
console.log('   2. 如果设置了 includeTables，只保留这些表');
console.log('   3. 如果设置了 excludeTables，从结果中排除这些表');
console.log('   4. 返回最终的表列表');
console.log('');

console.log('4️⃣ 实际使用示例');
console.log('------------------');
console.log(`
假设数据库有表: users, products, orders, logs, sessions, temp_data

示例 1 - 只排除:
excludeTables: [logs, sessions]
结果: users, products, orders, temp_data

示例 2 - 只包含:
includeTables: [users, products, orders]
结果: users, products, orders

示例 3 - 先包含后排除:
includeTables: [users, products, orders, logs]
excludeTables: [logs]
结果: users, products, orders

示例 4 - 命令行覆盖配置:
配置文件: excludeTables: [logs]
命令行: --include users,products
结果: users, products (配置文件设置被覆盖)
`);

console.log('5️⃣ 常见应用场景');
console.log('------------------');
console.log('🏭 生产环境迁移:');
console.log('   - 排除敏感数据表（logs, sessions, audit_trail）');
console.log('   - 排除临时数据表（temp_*, cache_*）');
console.log('   - 排除系统表（PostGIS: spatial_ref_sys）');
console.log('');
console.log('🧪 开发测试环境:');
console.log('   - 只包含核心业务表（users, products, orders）');
console.log('   - 排除大数据表（analytics_*, reports_*）');
console.log('   - 保持数据库小巧便于开发');
console.log('');
console.log('⚡ 快速部署:');
console.log('   - 按模块迁移（用户模块、订单模块）');
console.log('   - 分阶段上线不同功能');
console.log('   - 减少迁移时间和风险');
console.log('');

console.log('6️⃣ 测试命令');
console.log('-------------');
console.log('🧪 运行表过滤测试:');
console.log('   node test-exclude-tables.js');
console.log('');
console.log('🔧 实际迁移测试:');
console.log('   # 使用测试配置');
console.log('   dbtool migrate -c config.test.yaml source_with_filter target_core_only ./test_migrations');
console.log('');
console.log('   # 命令行过滤测试');
console.log('   dbtool migrate --exclude sys_logs,temp_table db2 db2 ./test_migrations');
console.log('');

console.log('7️⃣ 注意事项');
console.log('-------------');
console.log('⚠️  重要提醒:');
console.log('   • 表名区分大小写，确保拼写正确');
console.log('   • 注意外键依赖关系，避免引用完整性问题');
console.log('   • 使用 includeTables 时确保包含所有关联表');
console.log('   • 源数据库和目标数据库的过滤配置独立应用');
console.log('   • 不存在的表名会被忽略，不会报错');
console.log('   • 建议在测试环境验证过滤效果');
console.log('');

console.log('🎉 表排除功能演示完成!');
console.log('');
console.log('💡 提示:');
console.log('   详细文档请查看: docs/table-filtering.md');
console.log('   配置示例请查看: config.example.yaml');
console.log('   运行 dbtool migrate --help 查看所有选项');