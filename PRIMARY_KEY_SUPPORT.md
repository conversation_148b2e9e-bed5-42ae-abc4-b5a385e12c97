# 主键支持实现文档

## 概述

本文档描述了在数据库迁移工具中实现的主键支持功能。该功能允许工具正确处理主键的创建、修改和删除操作。

## 实现的功能

### 1. 主键检测
- ✅ MySQL 主键检测：通过 `INFORMATION_SCHEMA.KEY_COLUMN_USAGE` 查询主键信息
- ✅ PostgreSQL 主键检测：通过 `pg_constraint` 系统表查询主键信息
- ✅ 支持单列主键和复合主键

### 2. 主键比较
- ✅ 检测新增主键（源有主键，目标无主键）
- ✅ 检测删除主键（源无主键，目标有主键）
- ✅ 检测主键修改（主键列发生变化）
- ✅ 正确处理复合主键的顺序比较

### 3. SQL 生成

#### MySQL 生成器
- ✅ 创建表时包含主键：`PRIMARY KEY (column1, column2)`
- ✅ 添加主键：`ALTER TABLE table ADD PRIMARY KEY (columns)`
- ✅ 删除主键：`ALTER TABLE table DROP PRIMARY KEY`

#### PostgreSQL 生成器
- ✅ 创建表时包含主键：`PRIMARY KEY ("column1", "column2")`
- ✅ 添加主键：`ALTER TABLE "public"."table" ADD PRIMARY KEY ("columns")`
- ✅ 删除主键：`ALTER TABLE "public"."table" DROP CONSTRAINT "table_pkey"`

## 代码变更

### 1. 类型定义 (`src/types/migration.ts`)
```typescript
// 新增主键变更操作类型
export enum MigrationType {
  // ... 现有类型
  ADD_PRIMARY_KEY = 'ADD_PRIMARY_KEY',
  DROP_PRIMARY_KEY = 'DROP_PRIMARY_KEY',
}

// 新增主键变更信息接口
export interface PrimaryKeyChange {
  type: 'ADD' | 'DROP' | 'MODIFY';
  oldPrimaryKey?: string[];
  newPrimaryKey?: string[];
}

// 扩展表修改信息接口
export interface TableModification {
  // ... 现有字段
  primaryKeyChange?: PrimaryKeyChange;
}
```

### 2. 比较器 (`src/migration/comparator.ts`)
- 新增 `comparePrimaryKeys()` 方法
- 更新 `compareTable()` 方法以包含主键比较
- 更新 `hasTableModifications()` 方法以检查主键变更

### 3. SQL 生成器 (`src/migration/generator.ts`)
- 基类新增抽象方法：`generateAddPrimaryKey()` 和 `generateDropPrimaryKey()`
- MySQL 生成器实现主键操作方法
- PostgreSQL 生成器实现主键操作方法
- 更新 `generateTableModifications()` 方法以处理主键变更

### 4. PostgreSQL 创建表修复
- 修复了 PostgreSQL 生成器在创建表时不包含主键的问题
- 现在 `generateCreateTable()` 方法正确生成主键约束

## 使用示例

### 创建带主键的表
```sql
-- MySQL
CREATE TABLE `users` (
  `id` INTEGER NOT NULL AUTO_INCREMENT,
  `username` VARCHAR(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- PostgreSQL
CREATE TABLE "public"."users" (
  "id" int4 NOT NULL,
  "username" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  PRIMARY KEY ("id")
);
```

### 主键变更操作
```sql
-- 添加主键
ALTER TABLE `users` ADD PRIMARY KEY (`id`);
ALTER TABLE "public"."users" ADD PRIMARY KEY ("id");

-- 删除主键
ALTER TABLE `users` DROP PRIMARY KEY;
ALTER TABLE "public"."users" DROP CONSTRAINT "users_pkey";

-- 修改主键（先删除后添加）
ALTER TABLE `user_roles` DROP PRIMARY KEY;
ALTER TABLE `user_roles` ADD PRIMARY KEY (`user_id`, `role_id`);
```

## 测试验证

已通过以下测试验证功能正确性：
1. ✅ 单列主键和复合主键的创建
2. ✅ 主键变更检测（添加、删除、修改）
3. ✅ MySQL 和 PostgreSQL 的 SQL 生成
4. ✅ 端到端迁移流程

## 注意事项

1. **PostgreSQL 主键约束命名**：删除主键时使用标准命名约定 `{table_name}_pkey`
2. **操作顺序**：主键删除在字段修改之前，主键添加在字段修改之后
3. **复合主键**：正确处理多列主键的顺序和比较
4. **向后兼容**：所有现有功能保持不变，仅新增主键支持

## 总结

主键支持功能已完全实现并集成到现有的数据库迁移工具中。该功能提供了完整的主键生命周期管理，包括检测、比较和 SQL 生成，支持 MySQL 和 PostgreSQL 两种数据库系统。
