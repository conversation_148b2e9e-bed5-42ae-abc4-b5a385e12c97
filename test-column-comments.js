import { PostgreSQLGenerator } from './dist/migration/generator.js';

console.log('🧪 测试 PostgreSQL 列注释生成功能');
console.log('=====================================');

const generator = new PostgreSQLGenerator();

// 测试表结构，包含注释字段
const testTable = {
  name: 'wms_gps_records',
  columns: [
    {
      name: 'id',
      type: 'CHARACTER VARYING(255)',
      nullable: false,
      comment: '主键ID'
    },
    {
      name: 'created_at',
      type: 'timestamp with time zone',
      nullable: false,
      comment: '创建时间'
    },
    {
      name: 'updated_at',
      type: 'timestamp with time zone',
      nullable: false,
      comment: '更新时间'
    },
    {
      name: 'created_by',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '创建人'
    },
    {
      name: 'updated_by',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '更新人'
    },
    {
      name: 'remark',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '备注'
    },
    {
      name: 'code',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '设备编码'
    },
    {
      name: 'location',
      type: 'geometry(POINT, 4326)',
      nullable: true,
      comment: '空间点位'
    },
    {
      name: 'longitude',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '经度'
    },
    {
      name: 'latitude',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '纬度'
    },
    {
      name: 'altitude',
      type: 'CHARACTER VARYING(255)',
      nullable: true,
      comment: '海拔'
    }
  ],
  indexes: [],
  primaryKey: ['id']
};

console.log('📋 测试 CREATE TABLE 语句（包含注释）:');
const createTableOp = generator.generateCreateTable(testTable);
console.log(createTableOp.sql);
console.log('');

console.log('📋 测试 ADD COLUMN 语句（包含注释）:');
const newColumnWithComment = {
  name: 'speed',
  type: 'CHARACTER VARYING(255)',
  nullable: true,
  comment: '速度'
};
const addColumnOp = generator.generateAddColumn('wms_gps_records', newColumnWithComment);
console.log(addColumnOp.sql);
console.log('');

console.log('📋 测试 ADD COLUMN 语句（不包含注释）:');
const newColumnWithoutComment = {
  name: 'direction',
  type: 'CHARACTER VARYING(255)',
  nullable: true
};
const addColumnOpNoComment = generator.generateAddColumn('wms_gps_records', newColumnWithoutComment);
console.log(addColumnOpNoComment.sql);
console.log('');

console.log('📋 测试 MODIFY COLUMN 语句（修改注释）:');
const oldColumn = {
  name: 'remark',
  type: 'CHARACTER VARYING(255)',
  nullable: true,
  comment: '备注'
};
const modifiedColumn = {
  name: 'remark',
  type: 'CHARACTER VARYING(500)',
  nullable: true,
  comment: '备注信息'
};
const modifyColumnOp = generator.generateModifyColumn('wms_gps_records', oldColumn, modifiedColumn);
console.log(modifyColumnOp.sql);
console.log('');

console.log('✅ PostgreSQL 列注释生成测试完成！');
console.log('');
console.log('🔍 验证要点:');
console.log('1. CREATE TABLE 语句后应包含 COMMENT ON COLUMN 语句');
console.log('2. ADD COLUMN 有注释时应包含 COMMENT ON COLUMN 语句');
console.log('3. MODIFY COLUMN 时如果注释发生变化应包含 COMMENT ON COLUMN 语句');
console.log('4. 注释中的单引号应正确转义');